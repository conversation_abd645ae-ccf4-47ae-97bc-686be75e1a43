"""
Enhanced Free Energy Machine (FEM) for Mixed Variable Types

This implementation extends the original FEM to handle continuous variables using
variational free energy minimization with Gaussian proxy distributions.

Key Features:
1. Binary variables: Original FEM approach with parameter m_i ∈ [-1, 1]
2. Continuous variables: Gaussian distributions N(μ_i, σ_i²)
3. Analytical moment computation for polynomial objectives
4. Mean-field approximation for variable coupling
5. Temperature annealing and automatic differentiation
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Callable
import math
from abc import ABC, abstractmethod

class VariableProxy(ABC):
    """Abstract base class for variable proxy distributions."""
    
    @abstractmethod
    def compute_moments(self) -> Dict[str, torch.Tensor]:
        """Compute statistical moments of the distribution."""
        pass
    
    @abstractmethod
    def compute_entropy(self) -> torch.Tensor:
        """Compute entropy of the distribution."""
        pass
    
    @abstractmethod
    def sample(self, num_samples: int) -> torch.Tensor:
        """Sample from the distribution."""
        pass

class BinaryVariableProxy(VariableProxy, nn.Module):
    """Proxy distribution for binary variables σ ∈ {-1, +1}."""
    
    def __init__(self, device: str = 'cuda'):
        super().__init__()
        self.device = device
        # Parameter m_i represents expectation value ⟨σ_i⟩
        self.m = nn.Parameter(torch.zeros(1, device=device))
    
    def compute_moments(self) -> Dict[str, torch.Tensor]:
        """Compute moments for binary variable."""
        # For binary σ ∈ {-1, +1}:
        # ⟨σ⟩ = m
        # ⟨σ²⟩ = 1 (always)
        # ⟨σ³⟩ = m
        # ⟨σ⁴⟩ = 1
        return {
            'mean': self.m,
            'second_moment': torch.ones_like(self.m),
            'third_moment': self.m,
            'fourth_moment': torch.ones_like(self.m)
        }
    
    def compute_entropy(self) -> torch.Tensor:
        """Compute binary entropy."""
        # p = (1 + m) / 2, entropy = -p log p - (1-p) log(1-p)
        p = (1 + self.m) / 2
        p = torch.clamp(p, 1e-8, 1 - 1e-8)  # Numerical stability
        entropy = -(p * torch.log(p) + (1 - p) * torch.log(1 - p))
        return entropy
    
    def sample(self, num_samples: int) -> torch.Tensor:
        """Sample binary values."""
        p = (1 + self.m) / 2
        samples = torch.bernoulli(p.expand(num_samples))
        return 2 * samples - 1  # Convert {0,1} to {-1,+1}
    
    def get_probability(self) -> torch.Tensor:
        """Get probability of +1 state."""
        return (1 + self.m) / 2

class ContinuousVariableProxy(VariableProxy, nn.Module):
    """Proxy distribution for continuous variables x ∈ ℝ."""
    
    def __init__(self, bounds: Tuple[float, float] = (-10.0, 10.0), device: str = 'cuda'):
        super().__init__()
        self.device = device
        self.bounds = bounds
        
        # Initialize mean to center of bounds
        init_mean = (bounds[0] + bounds[1]) / 2
        self.mu = nn.Parameter(torch.tensor(init_mean, device=device))
        
        # Log-parameterization for variance to ensure σ² > 0
        init_log_var = math.log((bounds[1] - bounds[0]) / 6)  # 3-sigma rule
        self.log_var = nn.Parameter(torch.tensor(init_log_var, device=device))
    
    @property
    def variance(self) -> torch.Tensor:
        """Get variance σ²."""
        return torch.exp(self.log_var)
    
    @property
    def std(self) -> torch.Tensor:
        """Get standard deviation σ."""
        return torch.sqrt(self.variance)
    
    def compute_moments(self) -> Dict[str, torch.Tensor]:
        """Compute Gaussian moments analytically."""
        mu = self.mu
        var = self.variance
        
        # Analytical moments for Gaussian N(μ, σ²):
        # ⟨x⟩ = μ
        # ⟨x²⟩ = μ² + σ²
        # ⟨x³⟩ = μ³ + 3μσ²
        # ⟨x⁴⟩ = μ⁴ + 6μ²σ² + 3σ⁴
        return {
            'mean': mu,
            'second_moment': mu**2 + var,
            'third_moment': mu**3 + 3*mu*var,
            'fourth_moment': mu**4 + 6*mu**2*var + 3*var**2
        }
    
    def compute_entropy(self) -> torch.Tensor:
        """Compute Gaussian entropy."""
        # Entropy = ½ log(2πeσ²)
        return 0.5 * (1 + math.log(2 * math.pi)) + 0.5 * self.log_var
    
    def sample(self, num_samples: int) -> torch.Tensor:
        """Sample from Gaussian distribution."""
        samples = torch.normal(
            self.mu.expand(num_samples),
            self.std.expand(num_samples)
        )
        # Apply bounds if necessary
        if self.bounds:
            samples = torch.clamp(samples, self.bounds[0], self.bounds[1])
        return samples
    
    def apply_bounds_penalty(self) -> torch.Tensor:
        """Soft penalty for staying within bounds."""
        if not self.bounds:
            return torch.tensor(0.0, device=self.device)
        
        lower, upper = self.bounds
        penalty = 0.0
        
        # Penalty for mean being outside bounds
        penalty += torch.relu(self.mu - upper) ** 2
        penalty += torch.relu(lower - self.mu) ** 2
        
        # Penalty for variance being too large (3-sigma rule)
        max_std = (upper - lower) / 6
        penalty += torch.relu(self.std - max_std) ** 2
        
        return penalty

class EnhancedFEM(nn.Module):
    """
    Enhanced Free Energy Machine supporting mixed variable types.
    
    Extends original FEM to handle both binary and continuous variables
    using variational free energy minimization.
    """
    
    def __init__(
        self,
        variable_specs: List[Dict],
        objective_terms: List[Dict],
        constraints: List[Dict] = None,
        device: str = 'cuda'
    ):
        """
        Initialize Enhanced FEM.
        
        Args:
            variable_specs: List of variable specifications
                [{'type': 'binary'/'continuous', 'bounds': (min, max), 'id': int}, ...]
            objective_terms: List of objective terms
                [{'type': 'linear'/'quadratic'/'cubic', 'variables': [ids], 'coefficient': float}, ...]
            constraints: List of constraint specifications
            device: Computation device
        """
        super().__init__()
        
        self.device = device
        self.variable_specs = variable_specs
        self.objective_terms = objective_terms
        self.constraints = constraints or []
        
        # Create variable proxies
        self.variables = nn.ModuleDict()
        for spec in variable_specs:
            var_id = str(spec['id'])
            if spec['type'] == 'binary':
                self.variables[var_id] = BinaryVariableProxy(device=device)
            elif spec['type'] == 'continuous':
                bounds = spec.get('bounds', (-10.0, 10.0))
                self.variables[var_id] = ContinuousVariableProxy(bounds=bounds, device=device)
            else:
                raise ValueError(f"Unknown variable type: {spec['type']}")
        
        # Temperature parameter for annealing
        self.temperature = nn.Parameter(torch.tensor(1.0, device=device))
        
        # Constraint penalty weights
        self.constraint_weights = nn.Parameter(
            torch.ones(len(self.constraints), device=device)
        )
    
    def compute_expected_energy(self) -> torch.Tensor:
        """Compute expected energy ⟨H⟩_q using analytical moments."""
        total_energy = torch.tensor(0.0, device=self.device)
        
        # Precompute all moments for efficiency
        all_moments = {}
        for var_id, proxy in self.variables.items():
            all_moments[var_id] = proxy.compute_moments()
        
        for term in self.objective_terms:
            energy_contribution = self._compute_term_expectation(term, all_moments)
            total_energy += energy_contribution
        
        return total_energy
    
    def _compute_term_expectation(self, term: Dict, all_moments: Dict) -> torch.Tensor:
        """Compute expectation of a single objective term."""
        term_type = term['type']
        variables = term['variables']
        coefficient = term['coefficient']
        
        if term_type == 'constant':
            return torch.tensor(coefficient, device=self.device)
        
        elif term_type == 'linear':
            # Linear term: c * x_i
            var_id = str(variables[0])
            moment = all_moments[var_id]['mean']
            return coefficient * moment
        
        elif term_type == 'quadratic':
            if len(variables) == 1:
                # Quadratic term: c * x_i²
                var_id = str(variables[0])
                moment = all_moments[var_id]['second_moment']
                return coefficient * moment
            else:
                # Cross term: c * x_i * x_j (mean-field approximation)
                var_i_id = str(variables[0])
                var_j_id = str(variables[1])
                moment_i = all_moments[var_i_id]['mean']
                moment_j = all_moments[var_j_id]['mean']
                return coefficient * moment_i * moment_j
        
        elif term_type == 'cubic':
            if len(variables) == 1:
                # Cubic term: c * x_i³
                var_id = str(variables[0])
                moment = all_moments[var_id]['third_moment']
                return coefficient * moment
            else:
                # Mixed cubic terms (mean-field approximation)
                if len(variables) == 2:
                    # c * x_i² * x_j
                    var_i_id = str(variables[0])
                    var_j_id = str(variables[1])
                    moment_i2 = all_moments[var_i_id]['second_moment']
                    moment_j = all_moments[var_j_id]['mean']
                    return coefficient * moment_i2 * moment_j
                else:
                    # c * x_i * x_j * x_k
                    product = torch.tensor(coefficient, device=self.device)
                    for var_id in variables:
                        moment = all_moments[str(var_id)]['mean']
                        product *= moment
                    return product
        
        elif term_type == 'quartic':
            if len(variables) == 1:
                # Quartic term: c * x_i⁴
                var_id = str(variables[0])
                moment = all_moments[var_id]['fourth_moment']
                return coefficient * moment
            else:
                # Mixed quartic terms (mean-field approximation)
                product = torch.tensor(coefficient, device=self.device)
                for var_id in variables:
                    moment = all_moments[str(var_id)]['mean']
                    product *= moment
                return product
        
        else:
            raise ValueError(f"Unknown term type: {term_type}")
    
    def compute_total_entropy(self) -> torch.Tensor:
        """Compute total entropy S(q) = Σᵢ S(qᵢ)."""
        total_entropy = torch.tensor(0.0, device=self.device)
        
        for proxy in self.variables.values():
            entropy = proxy.compute_entropy()
            total_entropy += entropy
        
        return total_entropy
    
    def compute_constraint_violations(self) -> torch.Tensor:
        """Compute constraint violation penalties."""
        if not self.constraints:
            return torch.tensor(0.0, device=self.device)
        
        total_violation = torch.tensor(0.0, device=self.device)
        
        for i, constraint in enumerate(self.constraints):
            violation = self._compute_single_constraint_violation(constraint)
            total_violation += self.constraint_weights[i] * violation
        
        return total_violation
    
    def _compute_single_constraint_violation(self, constraint: Dict) -> torch.Tensor:
        """Compute violation for a single constraint."""
        constraint_type = constraint['type']
        
        if constraint_type == 'linear_eq':
            # Linear equality: Σᵢ aᵢ⟨xᵢ⟩ = b
            lhs = torch.tensor(0.0, device=self.device)
            for var_id, coeff in constraint['coefficients'].items():
                proxy = self.variables[str(var_id)]
                mean = proxy.compute_moments()['mean']
                lhs += coeff * mean
            
            target = constraint['target']
            return (lhs - target) ** 2
        
        elif constraint_type == 'linear_ineq':
            # Linear inequality: Σᵢ aᵢ⟨xᵢ⟩ ≤ b
            lhs = torch.tensor(0.0, device=self.device)
            for var_id, coeff in constraint['coefficients'].items():
                proxy = self.variables[str(var_id)]
                mean = proxy.compute_moments()['mean']
                lhs += coeff * mean
            
            target = constraint['target']
            return torch.relu(lhs - target) ** 2
        
        else:
            raise ValueError(f"Unknown constraint type: {constraint_type}")
    
    def compute_bounds_penalties(self) -> torch.Tensor:
        """Compute penalties for variables exceeding bounds."""
        total_penalty = torch.tensor(0.0, device=self.device)
        
        for proxy in self.variables.values():
            if isinstance(proxy, ContinuousVariableProxy):
                penalty = proxy.apply_bounds_penalty()
                total_penalty += penalty
        
        return total_penalty
    
    def free_energy(self) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """Compute variational free energy F = ⟨H⟩_q - T·S(q)."""
        
        # Expected energy
        expected_energy = self.compute_expected_energy()
        
        # Entropy
        entropy = self.compute_total_entropy()
        
        # Constraint violations
        constraint_violations = self.compute_constraint_violations()
        
        # Bounds penalties
        bounds_penalties = self.compute_bounds_penalties()
        
        # Free energy with penalties
        free_energy = (expected_energy - self.temperature * entropy + 
                      constraint_violations + bounds_penalties)
        
        info = {
            'expected_energy': expected_energy,
            'entropy': entropy,
            'constraint_violations': constraint_violations,
            'bounds_penalties': bounds_penalties,
            'temperature': self.temperature
        }
        
        return free_energy, info
    
    def get_solution(self) -> Dict[str, Union[float, torch.Tensor]]:
        """Get current solution (mean values of distributions)."""
        solution = {}
        
        for var_id, proxy in self.variables.items():
            moments = proxy.compute_moments()
            solution[f'var_{var_id}'] = moments['mean'].item()
        
        return solution
    
    def sample_solution(self, num_samples: int = 1000) -> Dict[str, torch.Tensor]:
        """Sample solutions from current distributions."""
        samples = {}
        
        for var_id, proxy in self.variables.items():
            samples[f'var_{var_id}'] = proxy.sample(num_samples)
        
        return samples
