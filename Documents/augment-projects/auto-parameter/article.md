# 教程17 | 量子启发式算法结合自动调参工具

量子计算HiQ 2025年08月22日 19:51

[M]昇思
MindSpore Quantum 系列教程17
高级使用指南一量子启发式算法结合自动调参工具

![Optuna示例:参数一目标关系](https://user-images.githubusercontent.com/110151696/196985911-44e67b08-c6b7-4f7c-98a4-23f5f28095c8.png)

## 量子启发式算法

量子启发式算法是一类基于量子力学原理的计算方法衍生或启发的经典力学方法,旨在利用量子力学的独特性质(叠加态、量子纠缠和量子并行性)来改进传统算法的性能。比较有名的是Ewin tang受HHL启发提出的算法,但目前没有实用场景。为了便于区分,我们将受量子退火或者模拟量子Ising机,称为量子退火启发式算法。研究这类算法的意义在于不断探索经典算法的上界;其次可以对现实问题进行建模,使其能够被量子算法或者量子启发式算法进行求解,并且后续可以用QPU来代替启发式算法进行加速。

常见的量子启发式算法包括:

*   ASB (Adiabatic Simulated bifurcation/绝热模拟分叉算法)
*   BSB (Ballistic Simulated bifurcation/弹道模拟分叉算法)
*   DSB (Discrete Simulated bifurcation/离散模拟分叉算法)
*   SimCIM (Simulated Coherent Ising Machine/模拟相干伊辛机算法)
*   LQA (Local Quantum Annealing/局部量子退火算法)

MindSpore Quantum是基于昇思MindSpore开源深度学习平台开发的新一代通用量子计算框架,聚焦于NISQ阶段的算法实现与落地。结合HiQ高性能量子计算模拟器和昇思MindSpore并行自动微分能力,提供极简的开发模式和极致的性能体验。

MindSpore Quantum已经集成量子启发式算法模块,并提供CPU、GPU、NUP/昇腾版

本,适配多种硬件设备,并提供极致性能。

以BSB/绝热模拟分叉算法为例,介绍量子启发式算法中参数定义:

*   `J` (Union[numpy.array, scipy.sparse.spmatrix]) – 耦合矩阵,维度为(N\*N);与求解的图、ising或qubo问题相关。
*   `h` (numpy.array) – 外场强度,维度为(N,)。
*   `x` (numpy.array) – 自旋初始化配置,维度为(N\*batch\_size)。会在优化过程中被修改。如果不提供(None),将被初始化为在 [-0.01, 0.01] 范围内均匀分布的随机值。默认值:None。
*   `n_iter` (int) – 迭代步数。默认值:1000。
*   `batch_size` (int) - 样本个数。默认值:1。
*   `dt` (float) – 迭代步长。默认值:1。
*   `xi` (float) - 频率维数,正的常数。默认值: None。
*   `backend` (str) – 计算后端和精度: 'cpu-float32', 'gpu-float16' 或 'gpu-int8',默认值: 'cpu-float32',适配CPU/GPU不同的硬件设备。

可优化参数:

*   `n_iter` 迭代步数表示迭代计算的步数,根据具体问题来设置,迭代步数越大,越容易收敛,求解效果越好,但是计算时间越长,可调参数。
*   `batch_size` 样本个数,MindQuantum.qaia模块通过矩阵升维,提供并行化能力,样本个数越大,解的规模越大,计算时间越长,可调参数。
*   `dt` 迭代步长,控制每次动力学演化的步长距离,迭代步长直接影响到算法收敛的速度和稳定性;如果dt太大,可能会导致算法发散或不稳定;如果dt太小,则算法收敛速度会较慢,可调参数。
*   `xi` 频率维数,用于调整算法在频率空间上的特性,可调参数。

综合考虑算法的稳定性、收敛速度、问题特性以及算法背景等因素,选择合适的参数 (`n_iter`、`batch_size`、`dt`、`xi`)。

通常选取一组较好的参数组合需要大规模运行实验,耗费大量时间和人力,自动化调参工具便应运而生。

## 自动调参工具Optuna和Hyperopt

目前业界使用最广泛的python调参工具是Optuna和Hyperopt,可以使用网格搜索等方法自动化得到目标函数的最佳结果。

### Optuna

Optuna 是一个开源超参数优化框架,由Preferred Networks开发,适合机器学习和深度学习,并逐步适配LLM大模型。相比传统的网格搜索Grid Search, Optuna使用贝叶斯优化等算法,能够更高效地找到最优参数组合。

主要特点:

*   大规模搜索空间,支持多种参数类型 (连续、离散、分类)
*   先进高效的参数搜索算法
*   提供可视化和并行优化能力
*   支持对接PyTorch和TensorFlow等深度学习框架

常用API:

*   Trial: 目标函数的单次调用
*   Study: 一次优化过程,包含一系列的 trials.
*   Parameter: 待优化的参数

安装命令:

通过pip安装,支持Python 3.8或更高版本 (Optuna官网: https://optuna.org/)

```bash
pip install optuna
```

```python
import optuna

def objective(trial):
    x = trial.suggest_float("x", -10, 10)
    return (x - 2) ** 2

study = optuna.create_study()
study.optimize(objective, n_trials=30)
print("Best parameters:", study.best_params)
```

将以上输出绘制成参数一目标关系图,如下图所示:

可以看到,采样点逐步靠近解析曲线最低点,体现搜索/调参的收敛性。

### Hyperopt

Hyperopt是一个用于超参数优化的框架,由James Bergstra开发的分布式优化库,基于贝叶斯优化 (Tree-structured Parzen Estimator, TPE) 算法,能够高效搜索算法的最佳参数组合。

主要特点:

*   提供多种优化算法,随机搜索、Parzen估计器树TPE、自适应TPE等
*   支持Spark和MongoDB分布式计算

安装命令:

通过pip安装 (Hyperopt官网: https://hyperopt.github.io/hyperopt/)

```bash
pip install hyperopt
```

```python
from hyperopt import fmin, tpe, hp, Trials

def objective(params):
    x = params["x"]
    return (x - 2) ** 2

space = {"x": hp.uniform("x", -10, 10)}
trials = Trials()
best = fmin(fn=objective, space=space, algo=tpe.suggest, max_evals=30)
print("Best parameters:", best)
```
```
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████

### Optuna对比Hyperopt

Optuna和Hyperopt两个自动调参框架都支持主流的贝叶斯优化等参数搜索算法。其中Optuna提供可视化功能,原生支持并行多进程,属于后起之秀;Hyperopt并行化对接MongoDB数据库,适合中小规模的参数搜索任务。

综上所述,我们使用量子启发式算法结合Optuna调参框架,求解图最大割问题。

## 最佳实践

我们结合量子启发式算法和调参框架Optuna,面向GSet图计算最大割MAXCUT,找到最优参数组合。

工作流:

*   数据准备,此处以GSet图为例。
*   数据处理,将图转化为MindQuantum.qaia启发式算法中稀疏化矩阵的格式。
*   构造目标函数,定义参数搜索空间和优化项。我们以启发式算法求解最大割问题作为目标函数,选取迭代步数、频率维数、迭代步长作为超参数,并确认数据类型和范围,将每次计算出来的最大割值作为优化项。
*   Optuna设置优化方向(最大化)和优化次数,运行得到最佳参数组合和最大割值。

```python
import optuna
from mindquantum.algorithm.qaia import BSB
import numpy as np
import pandas as pd
from scipy.sparse import coo_matrix

import requests
graph_file = "https://web.stanford.edu/~yyye/yyye/Gset/G22"
response = requests.get(graph_file)
open("G22", "wb").write(response.content)
```

```python
def read_gset(filename, negate=True):
    graph = pd.read_csv(filename, sep=" ")
    n_v = int(graph.columns[0])
    n_e = int(graph.columns[1])
    assert n_e == graph.shape[0], "The number of edges is not matche
    G = coo_matrix(
        (
            np.concatenate([graph.iloc[:, -1], graph.iloc[:, -1]]),
            (
                np.concatenate([graph.iloc[:, 0] - 1, graph.iloc[:,
                np.concatenate([graph.iloc[:, 1] - 1, graph.iloc[:,
            ),
        ),
        shape=(n_v, n_v),
    )
    if negate:
        G = -G
    return G
G = read_gset("./G22")
```

```python
def objective(trial):
    n_iter = trial.suggest_categorical("n_iter", [200, 500, 1000, 15
    xi = trial.suggest_float("xi", 0.1, 2)
    dt = trial.suggest_float("dt", 0, 2)

    bsb = BSB(J=G, batch_size=100, n_iter=n_iter, xi=xi, dt=dt)
    bsb.update()
    cut_value = bsb.calc_cut()
    max_cut = max(cut_value)
    return max_cut

study = optuna.create_study(direction="maximize")
study.optimize(objective, n_trials=30)

print("Best parameters:", study.best_params)
print("Best cut:", study.best_value)
```

可以看出,随着实验推进,最优cut值被多次刷新,展示出调参收益。上述样例代码执行30次后,得到最佳参数组合,最大割值为13350。

注意,本篇教程只是提供样例代码,优化次数仅为30,调参工具的结果具有一定随机性;在实际应用中结合业务需求可适当放大参数范围。

```python
from mindquantum.utils.show_info import InfoTable
InfoTable("mindquantum", "scipy", "numpy")
```

| Software      | Version |
| ------------- | ------- |
| mindquantum   | 0.10.0  |
| scipy         | 1.11.3  |
| numpy         | 1.26.1  |
| **System**    | **Info**  |
| Python        | 3.10.13 |
| OS            | Linux x86_64 |
| Memory        | 810.22 GB |
| CPU Max Thread| 96      |
| Date          | Tue Jun 10 14:49:51 2025 |