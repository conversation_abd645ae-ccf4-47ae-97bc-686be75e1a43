import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Tuple, Optional, Callable
import numpy as np
import matplotlib.pyplot as plt
from differentiable_sbm import DifferentiableSBM
import time

class SBMParameterOptimizer:
    """
    Parameter optimizer for SBM using automatic differentiation.
    
    This class implements FEM-inspired automatic parameter optimization for SBM algorithms,
    using gradient-based optimization to learn optimal parameters.
    """
    
    def __init__(
        self,
        sbm_model: DifferentiableSBM,
        optimizer_type: str = 'adam',
        learning_rate: float = 0.01,
        loss_function: str = 'negative_max_cut',
        regularization: Dict[str, float] = None,
        scheduler_config: Dict = None
    ):
        """
        Initialize parameter optimizer.
        
        Args:
            sbm_model: Differentiable SBM model
            optimizer_type: Type of optimizer ('adam', 'rmsprop', 'sgd')
            learning_rate: Learning rate for parameter optimization
            loss_function: Loss function type
            regularization: Regularization weights for parameters
            scheduler_config: Learning rate scheduler configuration
        """
        self.model = sbm_model
        self.optimizer_type = optimizer_type
        self.learning_rate = learning_rate
        self.loss_function_type = loss_function
        
        # Default regularization
        if regularization is None:
            regularization = {
                'dt_l2': 0.001,
                'xi_l2': 0.001,
                'stability': 0.01,
            }
        self.regularization = regularization
        
        # Setup optimizer
        self.optimizer = self._setup_optimizer()
        
        # Setup scheduler
        if scheduler_config is not None:
            self.scheduler = self._setup_scheduler(scheduler_config)
        else:
            self.scheduler = None
            
        # Training history
        self.history = {
            'loss': [],
            'cut_value': [],
            'parameters': [],
            'regularization_loss': []
        }
    
    def _setup_optimizer(self) -> optim.Optimizer:
        """Setup parameter optimizer."""
        if self.optimizer_type.lower() == 'adam':
            return optim.Adam(self.model.parameters(), lr=self.learning_rate, 
                            betas=(0.9, 0.999), eps=1e-8)
        elif self.optimizer_type.lower() == 'rmsprop':
            return optim.RMSprop(self.model.parameters(), lr=self.learning_rate,
                               alpha=0.99, eps=1e-8, momentum=0.9)
        elif self.optimizer_type.lower() == 'sgd':
            return optim.SGD(self.model.parameters(), lr=self.learning_rate, momentum=0.9)
        else:
            raise ValueError(f"Unknown optimizer type: {self.optimizer_type}")
    
    def _setup_scheduler(self, config: Dict) -> optim.lr_scheduler._LRScheduler:
        """Setup learning rate scheduler."""
        scheduler_type = config.get('type', 'step')
        
        if scheduler_type == 'step':
            return optim.lr_scheduler.StepLR(
                self.optimizer, 
                step_size=config.get('step_size', 50),
                gamma=config.get('gamma', 0.5)
            )
        elif scheduler_type == 'cosine':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=config.get('T_max', 100),
                eta_min=config.get('eta_min', 1e-6)
            )
        elif scheduler_type == 'plateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=config.get('factor', 0.5),
                patience=config.get('patience', 10)
            )
        else:
            raise ValueError(f"Unknown scheduler type: {scheduler_type}")
    
    def loss_function(self, results: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute loss function for parameter optimization.
        
        Args:
            results: Results from SBM forward pass
            
        Returns:
            Loss tensor
        """
        if self.loss_function_type == 'negative_max_cut':
            # Maximize cut value by minimizing negative cut value
            primary_loss = -results['max_cut']
            
        elif self.loss_function_type == 'negative_mean_cut':
            # Maximize mean cut value
            primary_loss = -results['mean_cut']
            
        elif self.loss_function_type == 'cut_variance':
            # Minimize variance to encourage consistent solutions
            cut_values = results['cut_value']
            primary_loss = -results['mean_cut'] + 0.1 * torch.var(cut_values)
            
        elif self.loss_function_type == 'energy_minimization':
            # Minimize energy (for general Ising problems)
            primary_loss = torch.mean(results['energy'])
            
        else:
            raise ValueError(f"Unknown loss function: {self.loss_function_type}")
        
        # Add regularization
        reg_loss = self._compute_regularization(results['parameters'])
        
        total_loss = primary_loss + reg_loss
        
        return total_loss, primary_loss, reg_loss
    
    def _compute_regularization(self, params: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Compute regularization loss."""
        reg_loss = torch.tensor(0.0, device=self.model.device)
        
        # L2 regularization on parameters
        if 'dt_l2' in self.regularization and 'dt' in params:
            reg_loss += self.regularization['dt_l2'] * (params['dt'] - 1.0) ** 2
            
        if 'xi_l2' in self.regularization and 'xi' in params:
            # Regularize xi around its default value
            xi_default = 0.5 * np.sqrt(self.model.N - 1) / torch.sqrt((self.model.J ** 2).sum())
            reg_loss += self.regularization['xi_l2'] * (params['xi'] - xi_default) ** 2
        
        # Stability regularization (penalize extreme parameter values)
        if 'stability' in self.regularization:
            for param_name, param_value in params.items():
                if param_name in ['dt', 'xi']:
                    # Penalize values that are too large (potential instability)
                    reg_loss += self.regularization['stability'] * torch.relu(param_value - 5.0) ** 2
                    # Penalize values that are too small (potential inefficiency)
                    reg_loss += self.regularization['stability'] * torch.relu(0.01 - param_value) ** 2
        
        return reg_loss
    
    def train_step(self) -> Dict[str, float]:
        """Perform one training step."""
        self.optimizer.zero_grad()
        
        # Forward pass
        results = self.model()
        
        # Compute loss
        total_loss, primary_loss, reg_loss = self.loss_function(results)
        
        # Backward pass
        total_loss.backward()
        
        # Gradient clipping for stability
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        # Optimizer step
        self.optimizer.step()
        
        # Scheduler step
        if self.scheduler is not None:
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(total_loss)
            else:
                self.scheduler.step()
        
        return {
            'total_loss': total_loss.item(),
            'primary_loss': primary_loss.item(),
            'reg_loss': reg_loss.item(),
            'cut_value': results['max_cut'].item(),
            'parameters': {k: v.item() for k, v in results['parameters'].items()}
        }
    
    def train(self, num_epochs: int, validation_problems: List = None, 
              verbose: bool = True, plot_progress: bool = False) -> Dict:
        """
        Train SBM parameters using automatic differentiation.
        
        Args:
            num_epochs: Number of training epochs
            validation_problems: List of validation problems for generalization testing
            verbose: Whether to print progress
            plot_progress: Whether to plot training progress
            
        Returns:
            Training results and final parameters
        """
        start_time = time.time()
        
        for epoch in range(num_epochs):
            # Training step
            step_results = self.train_step()
            
            # Record history
            self.history['loss'].append(step_results['total_loss'])
            self.history['cut_value'].append(step_results['cut_value'])
            self.history['parameters'].append(step_results['parameters'].copy())
            self.history['regularization_loss'].append(step_results['reg_loss'])
            
            # Validation
            if validation_problems is not None and epoch % 10 == 0:
                val_results = self.validate(validation_problems)
                if verbose:
                    print(f"Epoch {epoch}: Loss={step_results['total_loss']:.4f}, "
                          f"Cut={step_results['cut_value']:.2f}, "
                          f"Val_Cut={val_results['mean_cut']:.2f}")
            elif verbose and epoch % 10 == 0:
                print(f"Epoch {epoch}: Loss={step_results['total_loss']:.4f}, "
                      f"Cut={step_results['cut_value']:.2f}")
                print(f"  Parameters: {step_results['parameters']}")
        
        training_time = time.time() - start_time
        
        # Final results
        final_params = self.model.get_parameters()
        final_results = self.model()
        
        results = {
            'final_parameters': {k: v.item() for k, v in final_params.items()},
            'final_cut_value': final_results['max_cut'].item(),
            'training_time': training_time,
            'history': self.history
        }
        
        if plot_progress:
            self.plot_training_progress()
        
        return results
    
    def validate(self, validation_problems: List) -> Dict:
        """Validate on multiple problems."""
        self.model.eval()
        
        cut_values = []
        for J_val, h_val in validation_problems:
            # Temporarily replace problem
            original_J = self.model.J.clone()
            original_h = self.model.h.clone() if self.model.h is not None else None
            
            self.model.J = J_val.to(self.model.device)
            if h_val is not None:
                self.model.h = h_val.to(self.model.device)
            else:
                self.model.h = None
            
            with torch.no_grad():
                results = self.model()
                cut_values.append(results['max_cut'].item())
            
            # Restore original problem
            self.model.J = original_J
            self.model.h = original_h
        
        self.model.train()
        
        return {
            'cut_values': cut_values,
            'mean_cut': np.mean(cut_values),
            'std_cut': np.std(cut_values)
        }
    
    def plot_training_progress(self):
        """Plot training progress."""
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # Loss curve
        axes[0, 0].plot(self.history['loss'])
        axes[0, 0].set_title('Training Loss')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        
        # Cut value curve
        axes[0, 1].plot(self.history['cut_value'])
        axes[0, 1].set_title('Cut Value')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Max Cut Value')
        
        # Parameter evolution
        param_names = list(self.history['parameters'][0].keys())
        for i, param_name in enumerate(param_names[:2]):  # Plot first 2 parameters
            param_values = [p[param_name] for p in self.history['parameters']]
            axes[1, i].plot(param_values)
            axes[1, i].set_title(f'Parameter: {param_name}')
            axes[1, i].set_xlabel('Epoch')
            axes[1, i].set_ylabel(param_name)
        
        plt.tight_layout()
        plt.show()
    
    def compare_with_manual_tuning(self, manual_params: Dict, num_runs: int = 10) -> Dict:
        """
        Compare learned parameters with manually tuned parameters.
        
        Args:
            manual_params: Dictionary of manually tuned parameters
            num_runs: Number of runs for statistical comparison
            
        Returns:
            Comparison results
        """
        # Test learned parameters
        learned_cuts = []
        for _ in range(num_runs):
            results = self.model()
            learned_cuts.append(results['max_cut'].item())
        
        # Test manual parameters
        manual_cuts = []
        original_params = self.model.get_parameters()
        
        # Temporarily set manual parameters
        if 'dt' in manual_params and self.model.learnable_params.get('dt', False):
            self.model.dt_raw.data = torch.tensor(manual_params['dt'])
        if 'xi' in manual_params and self.model.learnable_params.get('xi', False):
            self.model.xi_raw.data = torch.tensor(manual_params['xi'])
        
        for _ in range(num_runs):
            results = self.model()
            manual_cuts.append(results['max_cut'].item())
        
        # Restore learned parameters
        for param_name, param_value in original_params.items():
            if param_name == 'dt' and self.model.learnable_params.get('dt', False):
                self.model.dt_raw.data = param_value
            elif param_name == 'xi' and self.model.learnable_params.get('xi', False):
                self.model.xi_raw.data = param_value
        
        return {
            'learned_cuts': {
                'mean': np.mean(learned_cuts),
                'std': np.std(learned_cuts),
                'max': np.max(learned_cuts),
                'values': learned_cuts
            },
            'manual_cuts': {
                'mean': np.mean(manual_cuts),
                'std': np.std(manual_cuts),
                'max': np.max(manual_cuts),
                'values': manual_cuts
            },
            'improvement': np.mean(learned_cuts) - np.mean(manual_cuts),
            'learned_params': {k: v.item() for k, v in original_params.items()},
            'manual_params': manual_params
        }
