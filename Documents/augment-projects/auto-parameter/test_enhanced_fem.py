#!/usr/bin/env python3
"""
Test Script for Enhanced Free Energy Machine

This script provides a simple test to verify the Enhanced FEM implementation
works correctly and demonstrates its key capabilities.
"""

import torch
import numpy as np
import sys
import traceback

def test_basic_functionality():
    """Test basic Enhanced FEM functionality."""
    print("=== Testing Basic Enhanced FEM Functionality ===")
    
    try:
        from enhanced_fem import EnhancedFEM, BinaryVariableProxy, ContinuousVariableProxy
        from enhanced_fem_optimizer import EnhancedFEMOptimizer, create_simple_polynomial_terms
        
        print("✅ Successfully imported Enhanced FEM modules")
        
        # Test 1: Create variable proxies
        print("\n--- Test 1: Variable Proxies ---")
        
        # Binary variable
        binary_proxy = BinaryVariableProxy()
        binary_moments = binary_proxy.compute_moments()
        binary_entropy = binary_proxy.compute_entropy()
        binary_samples = binary_proxy.sample(100)
        
        print(f"Binary proxy - Mean: {binary_moments['mean'].item():.4f}, "
              f"Entropy: {binary_entropy.item():.4f}, "
              f"Sample range: [{binary_samples.min().item():.1f}, {binary_samples.max().item():.1f}]")
        
        # Continuous variable
        continuous_proxy = ContinuousVariableProxy(bounds=(-2.0, 2.0))
        continuous_moments = continuous_proxy.compute_moments()
        continuous_entropy = continuous_proxy.compute_entropy()
        continuous_samples = continuous_proxy.sample(100)
        
        print(f"Continuous proxy - Mean: {continuous_moments['mean'].item():.4f}, "
              f"Variance: {continuous_proxy.variance.item():.4f}, "
              f"Entropy: {continuous_entropy.item():.4f}")
        
        print("✅ Variable proxies working correctly")
        
        # Test 2: Simple optimization problem
        print("\n--- Test 2: Simple Optimization ---")
        
        # Minimize: x² - 2x + 1 = (x-1)²
        # Analytical minimum: x* = 1, f* = 0
        
        variable_specs = [
            {'id': 0, 'type': 'continuous', 'bounds': (-5.0, 5.0)},
        ]
        
        objective_terms = create_simple_polynomial_terms(
            linear_coeffs={0: -2.0},  # -2x
            quadratic_coeffs={(0, 0): 1.0}  # x²
        )
        objective_terms.append({'type': 'constant', 'variables': [], 'coefficient': 1.0})
        
        model = EnhancedFEM(variable_specs, objective_terms)
        optimizer = EnhancedFEMOptimizer(model, learning_rate=0.05)
        
        result = optimizer.train(num_epochs=200, verbose=False)
        
        x_solution = result['solution']['var_0']
        final_objective = result['final_free_energy']
        
        print(f"Solution: x* = {x_solution:.6f} (analytical: 1.0)")
        print(f"Objective: f* = {final_objective:.6f} (analytical: 0.0)")
        print(f"Error: {abs(x_solution - 1.0):.6f}")
        
        if abs(x_solution - 1.0) < 0.1:
            print("✅ Simple optimization working correctly")
        else:
            print("❌ Simple optimization failed")
            return False
        
        # Test 3: Mixed variable problem
        print("\n--- Test 3: Mixed Variables ---")
        
        # Simple mixed problem: minimize σx where σ ∈ {-1, +1}, x ∈ [0, 2]
        # Optimal: σ = -1, x = 2 (to minimize -x)
        
        variable_specs = [
            {'id': 0, 'type': 'binary'},
            {'id': 1, 'type': 'continuous', 'bounds': (0.0, 2.0)},
        ]
        
        objective_terms = [
            {'type': 'quadratic', 'variables': [0, 1], 'coefficient': 1.0}  # σx
        ]
        
        model = EnhancedFEM(variable_specs, objective_terms)
        optimizer = EnhancedFEMOptimizer(model, learning_rate=0.02)
        
        result = optimizer.train(num_epochs=300, verbose=False)
        
        sigma_solution = result['solution']['var_0']
        x_solution = result['solution']['var_1']
        
        print(f"Solution: σ* = {sigma_solution:.4f}, x* = {x_solution:.4f}")
        print(f"Expected: σ* ≈ -1, x* ≈ 2 (to minimize σx)")
        
        if sigma_solution < 0 and x_solution > 1.5:
            print("✅ Mixed variable optimization working correctly")
        else:
            print("⚠️  Mixed variable optimization may need tuning")
        
        print("✅ All basic tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        traceback.print_exc()
        return False

def test_analytical_moments():
    """Test analytical moment computation."""
    print("\n=== Testing Analytical Moments ===")
    
    try:
        from enhanced_fem_optimizer import AnalyticalMoments
        
        # Test Gaussian moments
        mu = 1.5
        sigma2 = 0.8
        
        analytical_moments = AnalyticalMoments.gaussian_moments(mu, sigma2, max_order=4)
        
        print(f"Gaussian N({mu}, {sigma2}) moments:")
        for k, moment in analytical_moments.items():
            print(f"  ⟨x^{k}⟩ = {moment:.6f}")
        
        # Verify with numerical sampling
        np.random.seed(42)
        samples = np.random.normal(mu, np.sqrt(sigma2), 100000)
        
        print(f"\nNumerical verification (100k samples):")
        errors = []
        for k in range(1, 5):
            numerical = np.mean(samples**k)
            analytical = analytical_moments[k]
            error = abs(numerical - analytical)
            errors.append(error)
            print(f"  ⟨x^{k}⟩ = {numerical:.6f} (error: {error:.6f})")
        
        max_error = max(errors)
        if max_error < 0.01:
            print("✅ Analytical moments are accurate")
            return True
        else:
            print(f"❌ Analytical moments have large error: {max_error:.6f}")
            return False
            
    except Exception as e:
        print(f"❌ Analytical moments test failed: {e}")
        traceback.print_exc()
        return False

def test_constraint_handling():
    """Test constraint handling."""
    print("\n=== Testing Constraint Handling ===")
    
    try:
        from enhanced_fem import EnhancedFEM
        from enhanced_fem_optimizer import EnhancedFEMOptimizer, create_simple_polynomial_terms
        
        # Minimize: x² + y² subject to x + y = 2
        # Analytical solution: x* = y* = 1, f* = 2
        
        variable_specs = [
            {'id': 0, 'type': 'continuous', 'bounds': (-5.0, 5.0)},
            {'id': 1, 'type': 'continuous', 'bounds': (-5.0, 5.0)},
        ]
        
        objective_terms = create_simple_polynomial_terms(
            quadratic_coeffs={(0, 0): 1.0, (1, 1): 1.0}  # x² + y²
        )
        
        constraints = [
            {
                'type': 'linear_eq',
                'coefficients': {0: 1.0, 1: 1.0},
                'target': 2.0
            }
        ]
        
        model = EnhancedFEM(variable_specs, objective_terms, constraints)
        optimizer = EnhancedFEMOptimizer(model, learning_rate=0.03)
        
        result = optimizer.train(num_epochs=400, verbose=False)
        
        x_solution = result['solution']['var_0']
        y_solution = result['solution']['var_1']
        constraint_value = x_solution + y_solution
        
        print(f"Solution: x* = {x_solution:.4f}, y* = {y_solution:.4f}")
        print(f"Constraint x + y = {constraint_value:.4f} (should be 2.0)")
        print(f"Constraint violation: {abs(constraint_value - 2.0):.6f}")
        
        if abs(constraint_value - 2.0) < 0.1 and abs(x_solution - 1.0) < 0.2:
            print("✅ Constraint handling working correctly")
            return True
        else:
            print("⚠️  Constraint handling may need tuning")
            return False
            
    except Exception as e:
        print(f"❌ Constraint handling test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("ENHANCED FREE ENERGY MACHINE - TEST SUITE")
    print("=" * 50)
    
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Analytical Moments", test_analytical_moments),
        ("Constraint Handling", test_constraint_handling),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✅ PASSED" if passed_test else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced FEM is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check implementation or dependencies.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
