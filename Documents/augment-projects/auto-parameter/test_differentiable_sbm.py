"""
Test script for Differentiable SBM implementation.

This script tests the basic functionality of the differentiable SBM
and parameter optimization framework.
"""

import torch
import numpy as np
import networkx as nx
from differentiable_sbm import DifferentiableSBM
from sbm_parameter_optimizer import SBMParameterOptimizer

def test_basic_functionality():
    """Test basic functionality of DifferentiableSBM."""
    print("Testing basic functionality...")
    
    # Create simple test problem
    N = 10
    J = torch.randn(N, N)
    J = (J + J.T) / 2  # Make symmetric
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Test BSB
    print("Testing BSB...")
    model_bsb = DifferentiableSBM(
        J=J,
        algorithm='BSB',
        n_iter=100,
        batch_size=10,
        device=device,
        learnable_params={'dt': True, 'xi': True}
    )
    
    results_bsb = model_bsb()
    print(f"BSB - Cut value: {results_bsb['max_cut'].item():.3f}")
    print(f"BSB - Parameters: {results_bsb['parameters']}")
    
    # Test ASB
    print("\nTesting ASB...")
    model_asb = DifferentiableSBM(
        J=J,
        algorithm='ASB',
        n_iter=100,
        batch_size=10,
        device=device,
        learnable_params={'dt': True, 'xi': True}
    )
    
    results_asb = model_asb()
    print(f"ASB - Cut value: {results_asb['max_cut'].item():.3f}")
    print(f"ASB - Parameters: {results_asb['parameters']}")
    
    # Test DSB
    print("\nTesting DSB...")
    model_dsb = DifferentiableSBM(
        J=J,
        algorithm='DSB',
        n_iter=100,
        batch_size=10,
        device=device,
        learnable_params={'dt': True, 'xi': True}
    )
    
    results_dsb = model_dsb()
    print(f"DSB - Cut value: {results_dsb['max_cut'].item():.3f}")
    print(f"DSB - Parameters: {results_dsb['parameters']}")
    
    print("Basic functionality test completed successfully!")
    return True

def test_parameter_optimization():
    """Test parameter optimization functionality."""
    print("\nTesting parameter optimization...")
    
    # Create test problem
    N = 15
    G = nx.erdos_renyi_graph(N, 0.4)
    A = nx.adjacency_matrix(G).toarray().astype(np.float32)
    J = torch.tensor(A + A.T, dtype=torch.float32)  # Symmetric
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Create model
    model = DifferentiableSBM(
        J=J,
        algorithm='BSB',
        n_iter=200,
        batch_size=20,
        device=device,
        learnable_params={'dt': True, 'xi': True}
    )
    
    # Get initial parameters and performance
    initial_params = model.get_parameters()
    initial_results = model()
    print(f"Initial parameters: {initial_params}")
    print(f"Initial cut value: {initial_results['max_cut'].item():.3f}")
    
    # Create optimizer
    optimizer = SBMParameterOptimizer(
        sbm_model=model,
        optimizer_type='adam',
        learning_rate=0.02,
        loss_function='negative_max_cut',
        regularization={'dt_l2': 0.001, 'xi_l2': 0.001}
    )
    
    # Train for a few epochs
    print("\nStarting parameter optimization...")
    results = optimizer.train(num_epochs=20, verbose=True)
    
    # Compare results
    print(f"\nOptimization Results:")
    print(f"Initial cut value: {initial_results['max_cut'].item():.3f}")
    print(f"Final cut value: {results['final_cut_value']:.3f}")
    print(f"Improvement: {results['final_cut_value'] - initial_results['max_cut'].item():.3f}")
    print(f"Initial parameters: {initial_params}")
    print(f"Final parameters: {results['final_parameters']}")
    
    improvement = results['final_cut_value'] - initial_results['max_cut'].item()
    if improvement > 0:
        print("✓ Parameter optimization improved performance!")
    else:
        print("⚠ Parameter optimization did not improve performance (may need more epochs)")
    
    return True

def test_gradient_flow():
    """Test that gradients flow properly through the model."""
    print("\nTesting gradient flow...")
    
    # Create simple problem
    N = 8
    J = torch.eye(N) + 0.5 * torch.randn(N, N)
    J = (J + J.T) / 2
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    model = DifferentiableSBM(
        J=J,
        algorithm='BSB',
        n_iter=50,
        batch_size=5,
        device=device,
        learnable_params={'dt': True, 'xi': True}
    )
    
    # Check if parameters require gradients
    params = list(model.parameters())
    print(f"Number of learnable parameters: {len(params)}")
    
    for i, param in enumerate(params):
        print(f"Parameter {i}: shape={param.shape}, requires_grad={param.requires_grad}")
    
    # Forward pass
    results = model()
    loss = -results['max_cut']  # Minimize negative cut value
    
    print(f"Loss value: {loss.item():.3f}")
    
    # Backward pass
    loss.backward()
    
    # Check gradients
    gradients_exist = True
    for i, param in enumerate(params):
        if param.grad is not None:
            grad_norm = param.grad.norm().item()
            print(f"Parameter {i} gradient norm: {grad_norm:.6f}")
            if grad_norm == 0:
                print(f"⚠ Warning: Zero gradient for parameter {i}")
        else:
            print(f"✗ No gradient for parameter {i}")
            gradients_exist = False
    
    if gradients_exist:
        print("✓ Gradients flow properly through the model!")
    else:
        print("✗ Gradient flow issues detected!")
    
    return gradients_exist

def test_soft_approximations():
    """Test soft approximation functions."""
    print("\nTesting soft approximations...")
    
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Create model to access soft functions
    J = torch.eye(5)
    model = DifferentiableSBM(J=J, device=device)
    
    # Test soft sign
    x = torch.tensor([-2.0, -0.5, 0.0, 0.5, 2.0], device=device)
    soft_sign_result = model.soft_sign(x, temperature=1.0)
    hard_sign_result = torch.sign(x)
    
    print("Soft sign test:")
    print(f"Input: {x.cpu().numpy()}")
    print(f"Soft sign: {soft_sign_result.cpu().numpy()}")
    print(f"Hard sign: {hard_sign_result.cpu().numpy()}")
    
    # Test soft clip
    x_clip = torch.tensor([-2.0, -0.5, 0.0, 0.5, 2.0], device=device)
    soft_clip_result = model.soft_clip(x_clip, threshold=1.0)
    hard_clip_result = torch.clamp(x_clip, -1.0, 1.0)
    
    print("\nSoft clip test:")
    print(f"Input: {x_clip.cpu().numpy()}")
    print(f"Soft clip: {soft_clip_result.cpu().numpy()}")
    print(f"Hard clip: {hard_clip_result.cpu().numpy()}")
    
    # Check differentiability
    x_test = torch.tensor([1.5], device=device, requires_grad=True)
    soft_result = model.soft_sign(x_test)
    soft_result.backward()
    
    if x_test.grad is not None:
        print(f"\n✓ Soft sign is differentiable (gradient: {x_test.grad.item():.6f})")
    else:
        print("\n✗ Soft sign gradient issue")
    
    return True

def run_all_tests():
    """Run all tests."""
    print("=" * 60)
    print("RUNNING DIFFERENTIABLE SBM TESTS")
    print("=" * 60)
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Gradient Flow", test_gradient_flow),
        ("Soft Approximations", test_soft_approximations),
        ("Parameter Optimization", test_parameter_optimization),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running: {test_name}")
        print(f"{'-' * 40}")
        
        try:
            result = test_func()
            results[test_name] = result
            print(f"✓ {test_name} completed successfully")
        except Exception as e:
            print(f"✗ {test_name} failed with error: {e}")
            results[test_name] = False
            import traceback
            traceback.print_exc()
    
    # Summary
    print(f"\n{'=' * 60}")
    print("TEST SUMMARY")
    print(f"{'=' * 60}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The implementation is working correctly.")
    else:
        print("⚠ Some tests failed. Please check the implementation.")
    
    return results

if __name__ == "__main__":
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Run tests
    test_results = run_all_tests()
