"""
Hybrid FEM-SBM System for Mixed Variable Types

This implementation combines FEM and SBM approaches to handle problems with
mixed variable types (binary, integer, continuous) and complex constraints,
similar to D-Wave's CQM capabilities.

Key innovations:
1. FEM for discrete variables (binary, integer)
2. Continuous SBM for real variables
3. Coordinated optimization between subsystems
4. Unified constraint handling
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from continuous_fem import ContinuousFEM
from continuous_sbm import ContinuousSBM

class HybridFEMSBM(nn.Module):
    """
    Hybrid system combining FEM for discrete variables and SBM for continuous variables.
    
    This approach allows handling of mixed-integer nonlinear programming (MINLP)
    problems with both discrete and continuous variables.
    """
    
    def __init__(
        self,
        problem_spec: Dict,
        coordination_method: str = 'alternating',
        device: str = 'cuda'
    ):
        """
        Initialize Hybrid FEM-SBM system.
        
        Args:
            problem_spec: Problem specification dictionary
            coordination_method: How to coordinate subsystems ('alternating', 'simultaneous')
            device: Computation device
        """
        super().__init__()
        
        self.problem_spec = problem_spec
        self.coordination_method = coordination_method
        self.device = device
        
        # Separate variables by type
        self.discrete_vars, self.continuous_vars = self._separate_variables()
        
        # Create subsystems
        self.fem_system = self._create_fem_system() if self.discrete_vars else None
        self.sbm_system = self._create_sbm_system() if self.continuous_vars else None
        
        # Coordination parameters
        self.coordination_weight = nn.Parameter(torch.tensor(1.0))
        
    def _separate_variables(self) -> Tuple[List[int], List[int]]:
        """Separate variables into discrete and continuous."""
        discrete_vars = []
        continuous_vars = []
        
        variable_types = self.problem_spec['variable_types']
        
        for var_id, var_type in variable_types.items():
            if var_type in ['binary', 'integer']:
                discrete_vars.append(var_id)
            elif var_type == 'continuous':
                continuous_vars.append(var_id)
        
        return discrete_vars, continuous_vars
    
    def _create_fem_system(self) -> ContinuousFEM:
        """Create FEM subsystem for discrete variables."""
        
        # Extract discrete variable information
        discrete_variable_types = {i: self.problem_spec['variable_types'][var_id] 
                                 for i, var_id in enumerate(self.discrete_vars)}
        discrete_variable_bounds = {i: self.problem_spec['variable_bounds'][var_id]
                                  for i, var_id in enumerate(self.discrete_vars)}
        
        # Extract quadratic terms involving only discrete variables
        discrete_quadratic_terms = {}
        for (i, j), coeff in self.problem_spec['quadratic_terms'].items():
            if i in self.discrete_vars and j in self.discrete_vars:
                i_idx = self.discrete_vars.index(i)
                j_idx = self.discrete_vars.index(j)
                discrete_quadratic_terms[(i_idx, j_idx)] = coeff
        
        # Extract linear terms for discrete variables
        discrete_linear_terms = {}
        for var_id, coeff in self.problem_spec.get('linear_terms', {}).items():
            if var_id in self.discrete_vars:
                i_idx = self.discrete_vars.index(var_id)
                discrete_linear_terms[i_idx] = coeff
        
        # Extract constraints involving only discrete variables
        discrete_constraints = []
        for constraint in self.problem_spec.get('constraints', []):
            if self._constraint_involves_only_discrete(constraint):
                discrete_constraints.append(self._remap_constraint_to_discrete(constraint))
        
        return ContinuousFEM(
            num_variables=len(self.discrete_vars),
            variable_types=discrete_variable_types,
            variable_bounds=discrete_variable_bounds,
            quadratic_terms=discrete_quadratic_terms,
            linear_terms=discrete_linear_terms,
            constraints=discrete_constraints,
            device=self.device
        )
    
    def _create_sbm_system(self) -> ContinuousSBM:
        """Create SBM subsystem for continuous variables."""
        
        # Extract continuous variable bounds
        continuous_variable_bounds = {i: self.problem_spec['variable_bounds'][var_id]
                                    for i, var_id in enumerate(self.continuous_vars)}
        
        # Extract quadratic terms involving only continuous variables
        continuous_quadratic_terms = {}
        for (i, j), coeff in self.problem_spec['quadratic_terms'].items():
            if i in self.continuous_vars and j in self.continuous_vars:
                i_idx = self.continuous_vars.index(i)
                j_idx = self.continuous_vars.index(j)
                continuous_quadratic_terms[(i_idx, j_idx)] = coeff
        
        # Extract linear terms for continuous variables
        continuous_linear_terms = {}
        for var_id, coeff in self.problem_spec.get('linear_terms', {}).items():
            if var_id in self.continuous_vars:
                i_idx = self.continuous_vars.index(var_id)
                continuous_linear_terms[i_idx] = coeff
        
        # Extract constraints involving only continuous variables
        continuous_constraints = []
        for constraint in self.problem_spec.get('constraints', []):
            if self._constraint_involves_only_continuous(constraint):
                continuous_constraints.append(self._remap_constraint_to_continuous(constraint))
        
        return ContinuousSBM(
            num_variables=len(self.continuous_vars),
            variable_bounds=continuous_variable_bounds,
            quadratic_terms=continuous_quadratic_terms,
            linear_terms=continuous_linear_terms,
            constraints=continuous_constraints,
            device=self.device
        )
    
    def _constraint_involves_only_discrete(self, constraint: Dict) -> bool:
        """Check if constraint involves only discrete variables."""
        involved_vars = set()
        
        if 'coefficients' in constraint:
            involved_vars.update(constraint['coefficients'].keys())
        if 'linear_coefficients' in constraint:
            involved_vars.update(constraint['linear_coefficients'].keys())
        if 'quadratic_coefficients' in constraint:
            for (i, j) in constraint['quadratic_coefficients'].keys():
                involved_vars.update([i, j])
        
        return all(var_id in self.discrete_vars for var_id in involved_vars)
    
    def _constraint_involves_only_continuous(self, constraint: Dict) -> bool:
        """Check if constraint involves only continuous variables."""
        involved_vars = set()
        
        if 'coefficients' in constraint:
            involved_vars.update(constraint['coefficients'].keys())
        if 'linear_coefficients' in constraint:
            involved_vars.update(constraint['linear_coefficients'].keys())
        if 'quadratic_coefficients' in constraint:
            for (i, j) in constraint['quadratic_coefficients'].keys():
                involved_vars.update([i, j])
        
        return all(var_id in self.continuous_vars for var_id in involved_vars)
    
    def _remap_constraint_to_discrete(self, constraint: Dict) -> Dict:
        """Remap constraint variable indices to discrete subsystem."""
        remapped = constraint.copy()
        
        if 'coefficients' in constraint:
            remapped['coefficients'] = {
                self.discrete_vars.index(var_id): coeff
                for var_id, coeff in constraint['coefficients'].items()
                if var_id in self.discrete_vars
            }
        
        return remapped
    
    def _remap_constraint_to_continuous(self, constraint: Dict) -> Dict:
        """Remap constraint variable indices to continuous subsystem."""
        remapped = constraint.copy()
        
        if 'coefficients' in constraint:
            remapped['coefficients'] = {
                self.continuous_vars.index(var_id): coeff
                for var_id, coeff in constraint['coefficients'].items()
                if var_id in self.continuous_vars
            }
        
        return remapped
    
    def compute_coupling_energy(self, discrete_solution: Dict, continuous_solution: Dict) -> torch.Tensor:
        """Compute energy from coupling between discrete and continuous variables."""
        coupling_energy = torch.tensor(0.0, device=self.device)
        
        # Find quadratic terms that couple discrete and continuous variables
        for (i, j), coeff in self.problem_spec['quadratic_terms'].items():
            if (i in self.discrete_vars and j in self.continuous_vars) or \
               (i in self.continuous_vars and j in self.discrete_vars):
                
                # Get variable values
                if i in self.discrete_vars:
                    i_idx = self.discrete_vars.index(i)
                    val_i = discrete_solution[f'var_{i_idx}']
                else:
                    i_idx = self.continuous_vars.index(i)
                    val_i = continuous_solution[f'var_{i_idx}']
                
                if j in self.discrete_vars:
                    j_idx = self.discrete_vars.index(j)
                    val_j = discrete_solution[f'var_{j_idx}']
                else:
                    j_idx = self.continuous_vars.index(j)
                    val_j = continuous_solution[f'var_{j_idx}']
                
                coupling_energy += coeff * val_i * val_j
        
        return coupling_energy
    
    def alternating_optimization(self, num_iterations: int = 10) -> Dict:
        """Alternating optimization between FEM and SBM subsystems."""
        
        best_objective = float('inf')
        best_solution = None
        
        # Initialize solutions
        if self.fem_system:
            discrete_solution, _ = self.fem_system.get_solution()
        else:
            discrete_solution = {}
        
        if self.sbm_system:
            continuous_result = self.sbm_system.get_best_solution()
            continuous_solution = continuous_result['solution']
        else:
            continuous_solution = {}
        
        for iteration in range(num_iterations):
            
            # Optimize discrete variables (FEM) with fixed continuous variables
            if self.fem_system:
                # Add coupling terms as external fields
                self._update_fem_with_coupling(continuous_solution)
                
                # Optimize FEM
                fem_optimizer = self._create_fem_optimizer()
                fem_result = fem_optimizer.train(num_epochs=100, verbose=False)
                discrete_solution = fem_result['solution']
            
            # Optimize continuous variables (SBM) with fixed discrete variables
            if self.sbm_system:
                # Add coupling terms to SBM objective
                self._update_sbm_with_coupling(discrete_solution)
                
                # Optimize SBM
                continuous_result = self.sbm_system.get_best_solution()
                continuous_solution = continuous_result['solution']
            
            # Compute total objective
            total_objective = self._compute_total_objective(discrete_solution, continuous_solution)
            
            if total_objective < best_objective:
                best_objective = total_objective
                best_solution = {
                    'discrete': discrete_solution,
                    'continuous': continuous_solution,
                    'objective': total_objective
                }
        
        return best_solution
    
    def _update_fem_with_coupling(self, continuous_solution: Dict):
        """Update FEM system with coupling terms from continuous variables."""
        if not self.fem_system:
            return
        
        # Add coupling terms as modifications to linear terms
        # This is a simplified approach - more sophisticated coupling can be implemented
        pass
    
    def _update_sbm_with_coupling(self, discrete_solution: Dict):
        """Update SBM system with coupling terms from discrete variables."""
        if not self.sbm_system:
            return
        
        # Add coupling terms as modifications to linear terms
        # This is a simplified approach - more sophisticated coupling can be implemented
        pass
    
    def _create_fem_optimizer(self):
        """Create optimizer for FEM subsystem."""
        from continuous_fem import ContinuousFEMOptimizer
        return ContinuousFEMOptimizer(self.fem_system)
    
    def _compute_total_objective(self, discrete_solution: Dict, continuous_solution: Dict) -> float:
        """Compute total objective value."""
        total_obj = 0.0
        
        # Discrete part
        if self.fem_system and discrete_solution:
            discrete_samples = {k: torch.tensor([v]) for k, v in discrete_solution.items()}
            discrete_obj = self.fem_system.compute_objective(discrete_samples)
            total_obj += discrete_obj.item()
        
        # Continuous part
        if self.sbm_system and continuous_solution:
            # This would need to be computed properly
            pass
        
        # Coupling part
        coupling_energy = self.compute_coupling_energy(discrete_solution, continuous_solution)
        total_obj += coupling_energy.item()
        
        return total_obj
    
    def solve(self, method: str = 'alternating', **kwargs) -> Dict:
        """Solve the hybrid optimization problem."""
        
        if method == 'alternating':
            return self.alternating_optimization(**kwargs)
        else:
            raise ValueError(f"Unknown method: {method}")

# Example usage and problem specification
def create_example_problem() -> Dict:
    """Create an example mixed-variable problem similar to CQM."""
    
    return {
        'variable_types': {
            0: 'binary',      # Binary variable
            1: 'binary',      # Binary variable  
            2: 'integer',     # Integer variable
            3: 'continuous',  # Continuous variable
            4: 'continuous',  # Continuous variable
        },
        'variable_bounds': {
            0: (0, 1),        # Binary bounds
            1: (0, 1),        # Binary bounds
            2: (0, 10),       # Integer bounds
            3: (-5.0, 5.0),   # Continuous bounds
            4: (0.0, 10.0),   # Continuous bounds
        },
        'quadratic_terms': {
            (0, 1): 2.0,      # Binary-binary interaction
            (0, 3): 1.5,      # Binary-continuous interaction
            (1, 4): -1.0,     # Binary-continuous interaction
            (2, 3): 0.5,      # Integer-continuous interaction
            (3, 4): -2.0,     # Continuous-continuous interaction
        },
        'linear_terms': {
            0: 1.0,
            1: -0.5,
            2: 2.0,
            3: -1.0,
            4: 1.5,
        },
        'constraints': [
            {
                'type': 'linear_eq',
                'coefficients': {0: 1, 1: 1, 2: 1},
                'target': 2.0
            },
            {
                'type': 'linear_ineq',
                'coefficients': {3: 1, 4: 1},
                'target': 8.0
            }
        ]
    }

if __name__ == "__main__":
    # Example usage
    problem = create_example_problem()
    
    hybrid_solver = HybridFEMSBM(problem)
    solution = hybrid_solver.solve(method='alternating', num_iterations=20)
    
    print("Hybrid FEM-SBM Solution:")
    print(f"Discrete variables: {solution['discrete']}")
    print(f"Continuous variables: {solution['continuous']}")
    print(f"Total objective: {solution['objective']}")
