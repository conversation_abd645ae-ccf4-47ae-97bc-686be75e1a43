"""
Continuous Variable Extension of Simulated Bifurcation Machine (SBM)

This implementation extends SBM to handle real/continuous variables and quadratic 
interactions, inspired by D-Wave's CQM capabilities.

Key innovations:
1. Remove binary constraints from SBM dynamics
2. Add variable bounds through soft constraints
3. Support quadratic interactions between continuous variables
4. Constraint handling through penalty methods
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import math

class ContinuousSBM(nn.Module):
    """
    Continuous Variable Simulated Bifurcation Machine.
    
    Extends SBM's physics-inspired dynamics to continuous variables by:
    1. Removing hard clipping to {-1, +1}
    2. Adding soft boundary constraints
    3. Supporting general quadratic forms
    """
    
    def __init__(
        self,
        num_variables: int,
        variable_bounds: Dict[int, Tuple[float, float]],
        quadratic_terms: Dict[Tuple[int, int], float],
        linear_terms: Dict[int, float] = None,
        constraints: List[Dict] = None,
        algorithm: str = 'BSB',
        n_iter: int = 1000,
        batch_size: int = 100,
        device: str = 'cuda',
        dtype: torch.dtype = torch.float32
    ):
        """
        Initialize Continuous SBM.
        
        Args:
            num_variables: Number of variables
            variable_bounds: Bounds for each variable {var_id: (lower, upper)}
            quadratic_terms: Quadratic coefficients {(i,j): coeff}
            linear_terms: Linear coefficients {i: coeff}
            constraints: List of constraint dictionaries
            algorithm: SBM variant ('BSB', 'ASB', 'DSB')
            n_iter: Number of iterations
            batch_size: Number of parallel samples
            device: Computation device
            dtype: Data type
        """
        super().__init__()
        
        self.num_variables = num_variables
        self.variable_bounds = variable_bounds
        self.quadratic_terms = quadratic_terms
        self.linear_terms = linear_terms or {}
        self.constraints = constraints or []
        self.algorithm = algorithm
        self.n_iter = n_iter
        self.batch_size = batch_size
        self.device = device
        self.dtype = dtype
        
        # Create quadratic matrix Q and linear vector c
        self.Q, self.c = self._build_matrices()
        
        # SBM parameters (learnable)
        self.dt = nn.Parameter(torch.tensor(1.0, dtype=dtype))
        self.xi = nn.Parameter(torch.tensor(1.0, dtype=dtype))
        self.delta = nn.Parameter(torch.tensor(1.0, dtype=dtype))
        
        # Constraint penalty weights
        self.constraint_weights = nn.Parameter(torch.ones(len(self.constraints), dtype=dtype))
        
        # Boundary penalty strength
        self.boundary_penalty = nn.Parameter(torch.tensor(10.0, dtype=dtype))
        
    def _build_matrices(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """Build quadratic matrix Q and linear vector c from problem specification."""
        
        # Initialize quadratic matrix Q
        Q = torch.zeros(self.num_variables, self.num_variables, dtype=self.dtype, device=self.device)
        
        for (i, j), coeff in self.quadratic_terms.items():
            Q[i, j] = coeff
            if i != j:  # Make symmetric
                Q[j, i] = coeff
        
        # Initialize linear vector c
        c = torch.zeros(self.num_variables, dtype=self.dtype, device=self.device)
        for i, coeff in self.linear_terms.items():
            c[i] = coeff
        
        return Q, c
    
    def initialize_state(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """Initialize position and momentum variables."""
        
        # Initialize positions within bounds
        x = torch.zeros(self.num_variables, self.batch_size, dtype=self.dtype, device=self.device)
        
        for var_id in range(self.num_variables):
            if var_id in self.variable_bounds:
                lower, upper = self.variable_bounds[var_id]
                # Initialize uniformly within bounds
                x[var_id] = torch.rand(self.batch_size, dtype=self.dtype, device=self.device) * (upper - lower) + lower
            else:
                # Default initialization
                x[var_id] = 0.1 * torch.randn(self.batch_size, dtype=self.dtype, device=self.device)
        
        # Initialize momenta
        y = 0.01 * torch.randn(self.num_variables, self.batch_size, dtype=self.dtype, device=self.device)
        
        return x, y
    
    def compute_quadratic_force(self, x: torch.Tensor) -> torch.Tensor:
        """Compute force from quadratic terms: -∇(x^T Q x + c^T x)."""
        # Quadratic force: -2Qx - c
        force = -2.0 * torch.mm(self.Q, x) - self.c.unsqueeze(1)
        return force
    
    def compute_boundary_force(self, x: torch.Tensor) -> torch.Tensor:
        """Compute soft boundary constraint forces."""
        force = torch.zeros_like(x)
        
        for var_id in range(self.num_variables):
            if var_id in self.variable_bounds:
                lower, upper = self.variable_bounds[var_id]
                
                # Soft lower bound: penalty for x < lower
                lower_violation = torch.relu(lower - x[var_id])
                force[var_id] += self.boundary_penalty * lower_violation
                
                # Soft upper bound: penalty for x > upper
                upper_violation = torch.relu(x[var_id] - upper)
                force[var_id] -= self.boundary_penalty * upper_violation
        
        return force
    
    def compute_constraint_force(self, x: torch.Tensor) -> torch.Tensor:
        """Compute forces from general constraints."""
        if not self.constraints:
            return torch.zeros_like(x)
        
        total_force = torch.zeros_like(x)
        
        for i, constraint in enumerate(self.constraints):
            constraint_force = self._compute_single_constraint_force(constraint, x)
            total_force += self.constraint_weights[i] * constraint_force
        
        return total_force
    
    def _compute_single_constraint_force(self, constraint: Dict, x: torch.Tensor) -> torch.Tensor:
        """Compute force from a single constraint."""
        constraint_type = constraint['type']
        
        if constraint_type == 'linear_eq':
            # Linear equality: sum(a_i * x_i) = b
            # Force: -2 * a_i * (sum(a_j * x_j) - b)
            coeffs = constraint['coefficients']  # {var_id: coeff}
            target = constraint['target']
            
            # Compute constraint value
            constraint_val = torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
            for var_id, coeff in coeffs.items():
                constraint_val += coeff * x[var_id]
            
            violation = constraint_val - target
            
            # Compute force for each variable
            force = torch.zeros_like(x)
            for var_id, coeff in coeffs.items():
                force[var_id] = -2.0 * coeff * violation
            
            return force
        
        elif constraint_type == 'linear_ineq':
            # Linear inequality: sum(a_i * x_i) <= b
            # Force: -2 * a_i * max(0, sum(a_j * x_j) - b)
            coeffs = constraint['coefficients']
            target = constraint['target']
            
            constraint_val = torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
            for var_id, coeff in coeffs.items():
                constraint_val += coeff * x[var_id]
            
            violation = torch.relu(constraint_val - target)
            
            force = torch.zeros_like(x)
            for var_id, coeff in coeffs.items():
                force[var_id] = -2.0 * coeff * violation
            
            return force
        
        else:
            return torch.zeros_like(x)
    
    def bsb_step(self, x: torch.Tensor, y: torch.Tensor, p: float) -> Tuple[torch.Tensor, torch.Tensor]:
        """Single BSB evolution step."""
        
        # Compute total force
        quad_force = self.compute_quadratic_force(x)
        boundary_force = self.compute_boundary_force(x)
        constraint_force = self.compute_constraint_force(x)
        
        total_force = quad_force + boundary_force + constraint_force
        
        # BSB dynamics (modified for continuous variables)
        y_update = (-(self.delta - p) * x + self.xi * total_force) * self.dt
        y = y + y_update
        
        x_update = self.dt * y * self.delta
        x = x + x_update
        
        return x, y
    
    def asb_step(self, x: torch.Tensor, y: torch.Tensor, p: float) -> Tuple[torch.Tensor, torch.Tensor]:
        """Single ASB evolution step."""
        
        # Kerr nonlinearity coefficient
        K = 1.0
        
        # Compute forces
        quad_force = self.compute_quadratic_force(x)
        boundary_force = self.compute_boundary_force(x)
        constraint_force = self.compute_constraint_force(x)
        
        total_force = quad_force + boundary_force + constraint_force
        
        # ASB dynamics
        x_update = self.dt * y * self.delta
        y_update = (K * x**3 + (self.delta - p) * x - self.xi * total_force) * self.dt
        
        x = x + x_update
        y = y - y_update
        
        return x, y
    
    def forward(self, return_trajectory: bool = False) -> Dict[str, torch.Tensor]:
        """Run continuous SBM optimization."""
        
        x, y = self.initialize_state()
        
        # Pumping schedule
        p = torch.linspace(0, 1, self.n_iter, dtype=self.dtype, device=self.device)
        
        trajectory = [] if return_trajectory else None
        
        for i in range(self.n_iter):
            if self.algorithm == 'BSB':
                x, y = self.bsb_step(x, y, p[i])
            elif self.algorithm == 'ASB':
                x, y = self.asb_step(x, y, p[i])
            else:
                raise ValueError(f"Unknown algorithm: {self.algorithm}")
            
            if trajectory is not None:
                trajectory.append({'x': x.clone(), 'y': y.clone(), 'step': i})
        
        # Compute final metrics
        results = self._compute_metrics(x, y)
        
        if return_trajectory:
            results['trajectory'] = trajectory
        
        return results
    
    def _compute_metrics(self, x: torch.Tensor, y: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Compute optimization metrics."""
        
        # Objective value: x^T Q x + c^T x
        objective = torch.sum(x * torch.mm(self.Q, x), dim=0) + torch.sum(self.c.unsqueeze(1) * x, dim=0)
        
        # Boundary violations
        boundary_violations = torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
        for var_id in range(self.num_variables):
            if var_id in self.variable_bounds:
                lower, upper = self.variable_bounds[var_id]
                boundary_violations += torch.relu(lower - x[var_id]) + torch.relu(x[var_id] - upper)
        
        # Constraint violations
        constraint_violations = torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
        for constraint in self.constraints:
            violation = self._evaluate_constraint_violation(constraint, x)
            constraint_violations += violation
        
        return {
            'x_final': x,
            'y_final': y,
            'objective': objective,
            'best_objective': torch.min(objective),
            'mean_objective': torch.mean(objective),
            'boundary_violations': boundary_violations,
            'constraint_violations': constraint_violations,
            'total_violations': boundary_violations + constraint_violations,
            'feasible_solutions': (boundary_violations + constraint_violations) < 1e-6
        }
    
    def _evaluate_constraint_violation(self, constraint: Dict, x: torch.Tensor) -> torch.Tensor:
        """Evaluate constraint violation."""
        constraint_type = constraint['type']
        
        if constraint_type == 'linear_eq':
            coeffs = constraint['coefficients']
            target = constraint['target']
            
            lhs = torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
            for var_id, coeff in coeffs.items():
                lhs += coeff * x[var_id]
            
            return torch.abs(lhs - target)
        
        elif constraint_type == 'linear_ineq':
            coeffs = constraint['coefficients']
            target = constraint['target']
            
            lhs = torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
            for var_id, coeff in coeffs.items():
                lhs += coeff * x[var_id]
            
            return torch.relu(lhs - target)
        
        else:
            return torch.zeros(self.batch_size, dtype=self.dtype, device=self.device)
    
    def get_best_solution(self) -> Dict[str, Union[torch.Tensor, float]]:
        """Get the best solution from current run."""
        results = self.forward()
        
        # Find best feasible solution
        total_cost = results['objective'] + 1000 * results['total_violations']
        best_idx = torch.argmin(total_cost)
        
        solution = {}
        for var_id in range(self.num_variables):
            solution[f'var_{var_id}'] = results['x_final'][var_id, best_idx].item()
        
        return {
            'solution': solution,
            'objective': results['objective'][best_idx].item(),
            'violations': results['total_violations'][best_idx].item(),
            'is_feasible': results['feasible_solutions'][best_idx].item()
        }
