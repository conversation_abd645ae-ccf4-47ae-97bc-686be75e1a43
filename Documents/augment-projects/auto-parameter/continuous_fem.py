"""
Continuous Variable Extension of Free Energy Machine (FEM)

This implementation extends FEM to handle real/continuous variables and quadratic 
interactions, inspired by D-Wave's CQM capabilities.

Key innovations:
1. Gaussian mean-field approximation for continuous variables
2. Quadratic interaction support
3. Constraint handling through penalty methods
4. Hybrid discrete-continuous optimization
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import math

class ContinuousFEM(nn.Module):
    """
    Continuous Variable Free Energy Machine for real-valued optimization.
    
    Extends FEM's mean-field approach to continuous variables using Gaussian
    distributions instead of discrete marginal probabilities.
    """
    
    def __init__(
        self,
        num_variables: int,
        variable_types: Dict[int, str],  # {var_id: 'binary', 'integer', 'continuous'}
        variable_bounds: Dict[int, Tuple[float, float]],
        quadratic_terms: Dict[Tuple[int, int], float],
        linear_terms: Dict[int, float] = None,
        constraints: List[Dict] = None,
        device: str = 'cuda'
    ):
        """
        Initialize Continuous FEM.
        
        Args:
            num_variables: Total number of variables
            variable_types: Type of each variable
            variable_bounds: Bounds for each variable (lower, upper)
            quadratic_terms: Quadratic interaction coefficients {(i,j): coeff}
            linear_terms: Linear coefficients {i: coeff}
            constraints: List of constraint dictionaries
            device: Computation device
        """
        super().__init__()
        
        self.num_variables = num_variables
        self.variable_types = variable_types
        self.variable_bounds = variable_bounds
        self.quadratic_terms = quadratic_terms
        self.linear_terms = linear_terms or {}
        self.constraints = constraints or []
        self.device = device
        
        # Initialize parameters for different variable types
        self._init_variable_parameters()
        
        # Constraint penalty weights
        self.constraint_weights = nn.Parameter(torch.ones(len(self.constraints)))
        
    def _init_variable_parameters(self):
        """Initialize parameters for different variable types."""
        
        # For continuous variables: Gaussian parameters (mean, log_std)
        self.continuous_means = nn.ParameterDict()
        self.continuous_log_stds = nn.ParameterDict()
        
        # For discrete variables: logits for categorical distribution
        self.discrete_logits = nn.ParameterDict()
        
        # For binary variables: single logit
        self.binary_logits = nn.ParameterDict()
        
        for var_id in range(self.num_variables):
            var_type = self.variable_types.get(var_id, 'continuous')
            bounds = self.variable_bounds.get(var_id, (-10.0, 10.0))
            
            if var_type == 'continuous':
                # Initialize mean to center of bounds
                init_mean = (bounds[0] + bounds[1]) / 2
                self.continuous_means[str(var_id)] = nn.Parameter(
                    torch.tensor(init_mean, dtype=torch.float32)
                )
                # Initialize std to cover the range
                init_std = (bounds[1] - bounds[0]) / 6  # 3-sigma rule
                self.continuous_log_stds[str(var_id)] = nn.Parameter(
                    torch.log(torch.tensor(init_std, dtype=torch.float32))
                )
                
            elif var_type == 'binary':
                self.binary_logits[str(var_id)] = nn.Parameter(
                    torch.zeros(1, dtype=torch.float32)
                )
                
            elif var_type == 'integer':
                # Discrete integer variables
                num_values = int(bounds[1] - bounds[0] + 1)
                self.discrete_logits[str(var_id)] = nn.Parameter(
                    torch.zeros(num_values, dtype=torch.float32)
                )
    
    def sample_variables(self, num_samples: int = 1000) -> Dict[str, torch.Tensor]:
        """Sample variables from their current distributions."""
        samples = {}
        
        for var_id in range(self.num_variables):
            var_type = self.variable_types.get(var_id, 'continuous')
            bounds = self.variable_bounds.get(var_id, (-10.0, 10.0))
            
            if var_type == 'continuous':
                mean = self.continuous_means[str(var_id)]
                std = torch.exp(self.continuous_log_stds[str(var_id)])
                
                # Sample from Gaussian and clip to bounds
                raw_samples = torch.normal(mean.expand(num_samples), 
                                         std.expand(num_samples))
                samples[f'var_{var_id}'] = torch.clamp(raw_samples, bounds[0], bounds[1])
                
            elif var_type == 'binary':
                logits = self.binary_logits[str(var_id)]
                probs = torch.sigmoid(logits)
                samples[f'var_{var_id}'] = torch.bernoulli(
                    probs.expand(num_samples)
                ).float() * 2 - 1  # Convert to {-1, 1}
                
            elif var_type == 'integer':
                logits = self.discrete_logits[str(var_id)]
                probs = F.softmax(logits, dim=0)
                categorical = torch.multinomial(probs.expand(num_samples, -1), 1)
                samples[f'var_{var_id}'] = categorical.float() + bounds[0]
        
        return samples
    
    def compute_objective(self, samples: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Compute the quadratic objective function."""
        num_samples = list(samples.values())[0].shape[0]
        objective = torch.zeros(num_samples, device=self.device)
        
        # Linear terms
        for var_id, coeff in self.linear_terms.items():
            var_samples = samples[f'var_{var_id}']
            objective += coeff * var_samples
        
        # Quadratic terms
        for (i, j), coeff in self.quadratic_terms.items():
            var_i_samples = samples[f'var_{i}']
            var_j_samples = samples[f'var_{j}']
            objective += coeff * var_i_samples * var_j_samples
        
        return objective
    
    def compute_constraints(self, samples: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Compute constraint violations."""
        if not self.constraints:
            return torch.zeros(list(samples.values())[0].shape[0], device=self.device)
        
        num_samples = list(samples.values())[0].shape[0]
        total_violation = torch.zeros(num_samples, device=self.device)
        
        for i, constraint in enumerate(self.constraints):
            violation = self._evaluate_constraint(constraint, samples)
            total_violation += self.constraint_weights[i] * violation
        
        return total_violation
    
    def _evaluate_constraint(self, constraint: Dict, samples: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Evaluate a single constraint."""
        constraint_type = constraint['type']
        
        if constraint_type == 'linear_eq':
            # Linear equality: sum(a_i * x_i) = b
            coeffs = constraint['coefficients']  # {var_id: coeff}
            target = constraint['target']
            
            lhs = torch.zeros_like(list(samples.values())[0])
            for var_id, coeff in coeffs.items():
                lhs += coeff * samples[f'var_{var_id}']
            
            return (lhs - target) ** 2
        
        elif constraint_type == 'linear_ineq':
            # Linear inequality: sum(a_i * x_i) <= b
            coeffs = constraint['coefficients']
            target = constraint['target']
            
            lhs = torch.zeros_like(list(samples.values())[0])
            for var_id, coeff in coeffs.items():
                lhs += coeff * samples[f'var_{var_id}']
            
            return torch.relu(lhs - target) ** 2
        
        elif constraint_type == 'quadratic_eq':
            # Quadratic equality constraint
            linear_coeffs = constraint.get('linear_coefficients', {})
            quad_coeffs = constraint.get('quadratic_coefficients', {})
            target = constraint['target']
            
            lhs = torch.zeros_like(list(samples.values())[0])
            
            # Linear terms
            for var_id, coeff in linear_coeffs.items():
                lhs += coeff * samples[f'var_{var_id}']
            
            # Quadratic terms
            for (i, j), coeff in quad_coeffs.items():
                lhs += coeff * samples[f'var_{i}'] * samples[f'var_{j}']
            
            return (lhs - target) ** 2
        
        else:
            raise ValueError(f"Unknown constraint type: {constraint_type}")
    
    def compute_entropy(self) -> torch.Tensor:
        """Compute entropy of current variable distributions."""
        total_entropy = torch.tensor(0.0, device=self.device)
        
        for var_id in range(self.num_variables):
            var_type = self.variable_types.get(var_id, 'continuous')
            
            if var_type == 'continuous':
                # Gaussian entropy: 0.5 * log(2π e σ²)
                log_std = self.continuous_log_stds[str(var_id)]
                entropy = 0.5 * (1 + math.log(2 * math.pi)) + log_std
                total_entropy += entropy
                
            elif var_type == 'binary':
                # Binary entropy
                logits = self.binary_logits[str(var_id)]
                probs = torch.sigmoid(logits)
                entropy = -(probs * torch.log(probs + 1e-8) + 
                           (1 - probs) * torch.log(1 - probs + 1e-8))
                total_entropy += entropy
                
            elif var_type == 'integer':
                # Categorical entropy
                logits = self.discrete_logits[str(var_id)]
                probs = F.softmax(logits, dim=0)
                entropy = -torch.sum(probs * torch.log(probs + 1e-8))
                total_entropy += entropy
        
        return total_entropy
    
    def free_energy(self, num_samples: int = 1000, temperature: float = 1.0) -> torch.Tensor:
        """Compute variational free energy."""
        # Sample variables
        samples = self.sample_variables(num_samples)
        
        # Compute expected objective (energy)
        objective = self.compute_objective(samples)
        expected_objective = torch.mean(objective)
        
        # Compute constraint violations
        constraint_violations = self.compute_constraints(samples)
        expected_violations = torch.mean(constraint_violations)
        
        # Compute entropy
        entropy = self.compute_entropy()
        
        # Free energy = Expected Energy - Temperature * Entropy + Constraint Penalty
        free_energy = expected_objective - temperature * entropy + expected_violations
        
        return free_energy, {
            'expected_objective': expected_objective,
            'expected_violations': expected_violations,
            'entropy': entropy,
            'samples': samples
        }
    
    def get_solution(self, num_samples: int = 10000) -> Dict[str, float]:
        """Get the current best solution."""
        samples = self.sample_variables(num_samples)
        
        # Compute objective for all samples
        objectives = self.compute_objective(samples)
        constraint_violations = self.compute_constraints(samples)
        
        # Find best feasible solution (or least infeasible)
        total_cost = objectives + 1000 * constraint_violations  # Heavy penalty for violations
        best_idx = torch.argmin(total_cost)
        
        solution = {}
        for var_id in range(self.num_variables):
            var_samples = samples[f'var_{var_id}']
            solution[f'var_{var_id}'] = var_samples[best_idx].item()
        
        return solution, {
            'objective': objectives[best_idx].item(),
            'constraint_violation': constraint_violations[best_idx].item(),
            'is_feasible': constraint_violations[best_idx].item() < 1e-6
        }

class ContinuousFEMOptimizer:
    """Optimizer for Continuous FEM using automatic differentiation."""
    
    def __init__(
        self,
        model: ContinuousFEM,
        optimizer_type: str = 'adam',
        learning_rate: float = 0.01,
        temperature_schedule: str = 'linear'
    ):
        self.model = model
        self.optimizer_type = optimizer_type
        self.learning_rate = learning_rate
        self.temperature_schedule = temperature_schedule
        
        # Setup optimizer
        if optimizer_type == 'adam':
            self.optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        elif optimizer_type == 'rmsprop':
            self.optimizer = torch.optim.RMSprop(model.parameters(), lr=learning_rate)
        else:
            raise ValueError(f"Unknown optimizer: {optimizer_type}")
        
        # Training history
        self.history = {
            'free_energy': [],
            'objective': [],
            'constraint_violation': [],
            'temperature': []
        }
    
    def get_temperature(self, epoch: int, max_epochs: int) -> float:
        """Get temperature for current epoch."""
        if self.temperature_schedule == 'linear':
            return 1.0 - 0.9 * epoch / max_epochs  # 1.0 -> 0.1
        elif self.temperature_schedule == 'exponential':
            return 1.0 * (0.1 ** (epoch / max_epochs))
        else:
            return 1.0
    
    def train(self, num_epochs: int = 1000, num_samples: int = 1000, verbose: bool = True):
        """Train the continuous FEM model."""
        
        for epoch in range(num_epochs):
            self.optimizer.zero_grad()
            
            # Get current temperature
            temperature = self.get_temperature(epoch, num_epochs)
            
            # Compute free energy
            free_energy, info = self.model.free_energy(num_samples, temperature)
            
            # Backward pass
            free_energy.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # Optimizer step
            self.optimizer.step()
            
            # Record history
            self.history['free_energy'].append(free_energy.item())
            self.history['objective'].append(info['expected_objective'].item())
            self.history['constraint_violation'].append(info['expected_violations'].item())
            self.history['temperature'].append(temperature)
            
            if verbose and epoch % 100 == 0:
                print(f"Epoch {epoch}: Free Energy = {free_energy.item():.4f}, "
                      f"Objective = {info['expected_objective'].item():.4f}, "
                      f"Violations = {info['expected_violations'].item():.4f}, "
                      f"Temperature = {temperature:.4f}")
        
        # Get final solution
        solution, solution_info = self.model.get_solution()
        
        return {
            'solution': solution,
            'solution_info': solution_info,
            'history': self.history
        }
