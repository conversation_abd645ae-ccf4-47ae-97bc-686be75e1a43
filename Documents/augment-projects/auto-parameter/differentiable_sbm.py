import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Tuple, Optional, Callable
import math

class DifferentiableSBM(nn.Module):
    """
    Differentiable Simulated Bifurcation Machine for automatic parameter optimization.
    
    This implementation makes SBM parameters learnable through automatic differentiation,
    inspired by FEM's approach to parameter optimization.
    """
    
    def __init__(
        self,
        J: torch.Tensor,
        h: Optional[torch.Tensor] = None,
        algorithm: str = 'BSB',
        n_iter: int = 1000,
        batch_size: int = 100,
        device: str = 'cuda',
        dtype: torch.dtype = torch.float32,
        learnable_params: Dict[str, bool] = None,
        param_bounds: Dict[str, Tuple[float, float]] = None
    ):
        """
        Initialize Differentiable SBM.
        
        Args:
            J: Coupling matrix (N x N)
            h: External field (N x 1), optional
            algorithm: SBM variant ('BSB', 'ASB', 'DSB')
            n_iter: Number of iterations
            batch_size: Number of parallel samples
            device: Computation device
            dtype: Data type
            learnable_params: Dict specifying which parameters to learn
            param_bounds: Dict specifying parameter bounds
        """
        super().__init__()
        
        self.device = device
        self.dtype = dtype
        self.algorithm = algorithm
        self.n_iter = n_iter
        self.batch_size = batch_size
        
        # Problem setup
        self.N = J.shape[0]
        self.register_buffer('J', J.to(device=device, dtype=dtype))
        if h is not None:
            self.register_buffer('h', h.to(device=device, dtype=dtype))
        else:
            self.h = None
            
        # Default learnable parameters
        if learnable_params is None:
            learnable_params = {
                'dt': True,
                'xi': True,
                'delta': False,  # Usually kept fixed
                'M': False,      # Integer parameter, harder to optimize
            }
        
        # Default parameter bounds
        if param_bounds is None:
            param_bounds = {
                'dt': (0.01, 5.0),
                'xi': (0.01, 10.0),
                'delta': (0.5, 2.0),
                'M': (1, 10),
            }
        
        self.learnable_params = learnable_params
        self.param_bounds = param_bounds
        
        # Initialize learnable parameters
        self._init_parameters()
        
        # Soft approximations for discrete operations
        self.temperature = nn.Parameter(torch.tensor(1.0))  # For soft sign
        self.clip_temperature = nn.Parameter(torch.tensor(10.0))  # For soft clipping
        
    def _init_parameters(self):
        """Initialize learnable parameters with reasonable defaults."""
        
        # Time step parameter
        if self.learnable_params.get('dt', False):
            dt_init = 1.0
            self.dt_raw = nn.Parameter(torch.tensor(dt_init))
        else:
            self.register_buffer('dt', torch.tensor(1.0))
            
        # Frequency parameter  
        if self.learnable_params.get('xi', False):
            # Initialize xi based on problem structure (similar to original implementation)
            xi_init = 0.5 * math.sqrt(self.N - 1) / torch.sqrt((self.J ** 2).sum()).item()
            self.xi_raw = nn.Parameter(torch.tensor(xi_init))
        else:
            xi_default = 0.5 * math.sqrt(self.N - 1) / torch.sqrt((self.J ** 2).sum()).item()
            self.register_buffer('xi', torch.tensor(xi_default))
            
        # Detuning frequency
        if self.learnable_params.get('delta', False):
            self.delta_raw = nn.Parameter(torch.tensor(1.0))
        else:
            self.register_buffer('delta', torch.tensor(1.0))
            
        # M parameter for ASB (discrete, so we'll use Gumbel-Softmax if learnable)
        if self.learnable_params.get('M', False) and self.algorithm == 'ASB':
            # Use categorical distribution for discrete M
            M_logits = torch.zeros(10)  # Support M from 1 to 10
            M_logits[1] = 1.0  # Initialize to M=2
            self.M_logits = nn.Parameter(M_logits)
        else:
            self.register_buffer('M', torch.tensor(2))
    
    def get_parameters(self) -> Dict[str, torch.Tensor]:
        """Get current parameter values with proper bounds."""
        params = {}
        
        if self.learnable_params.get('dt', False):
            dt_min, dt_max = self.param_bounds['dt']
            params['dt'] = torch.sigmoid(self.dt_raw) * (dt_max - dt_min) + dt_min
        else:
            params['dt'] = self.dt
            
        if self.learnable_params.get('xi', False):
            xi_min, xi_max = self.param_bounds['xi']
            params['xi'] = torch.sigmoid(self.xi_raw) * (xi_max - xi_min) + xi_min
        else:
            params['xi'] = self.xi
            
        if self.learnable_params.get('delta', False):
            delta_min, delta_max = self.param_bounds['delta']
            params['delta'] = torch.sigmoid(self.delta_raw) * (delta_max - delta_min) + delta_min
        else:
            params['delta'] = self.delta
            
        if self.learnable_params.get('M', False) and self.algorithm == 'ASB':
            # Use Gumbel-Softmax for differentiable discrete sampling
            M_probs = F.gumbel_softmax(self.M_logits, tau=1.0, hard=False)
            params['M'] = torch.sum(M_probs * torch.arange(1, 11, device=self.device, dtype=self.dtype))
        else:
            params['M'] = self.M.float()
            
        return params
    
    def soft_sign(self, x: torch.Tensor, temperature: float = None) -> torch.Tensor:
        """Differentiable approximation of sign function using tanh."""
        if temperature is None:
            temperature = self.temperature
        return torch.tanh(temperature * x)
    
    def soft_clip(self, x: torch.Tensor, threshold: float = 1.0) -> torch.Tensor:
        """Differentiable approximation of hard clipping."""
        # Use smooth approximation: x * sigmoid(k * (threshold - |x|))
        k = self.clip_temperature
        mask = torch.sigmoid(k * (threshold - torch.abs(x)))
        return x * mask + torch.sign(x) * threshold * (1 - mask)
    
    def initialize_state(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """Initialize spin and momentum variables."""
        x = 0.02 * (torch.rand(self.N, self.batch_size, device=self.device, dtype=self.dtype) - 0.5)
        y = 0.02 * (torch.rand(self.N, self.batch_size, device=self.device, dtype=self.dtype) - 0.5)
        return x, y
    
    def forward(self, return_trajectory: bool = False) -> Dict[str, torch.Tensor]:
        """
        Forward pass through differentiable SBM.
        
        Args:
            return_trajectory: Whether to return full trajectory or just final state
            
        Returns:
            Dictionary containing results and metrics
        """
        params = self.get_parameters()
        x, y = self.initialize_state()
        
        # Pumping schedule
        p = torch.linspace(0, 1, self.n_iter, device=self.device, dtype=self.dtype)
        
        trajectory = [] if return_trajectory else None
        
        if self.algorithm == 'BSB':
            x, y = self._bsb_evolution(x, y, params, p, trajectory)
        elif self.algorithm == 'ASB':
            x, y = self._asb_evolution(x, y, params, p, trajectory)
        elif self.algorithm == 'DSB':
            x, y = self._dsb_evolution(x, y, params, p, trajectory)
        else:
            raise ValueError(f"Unknown algorithm: {self.algorithm}")
        
        # Calculate final metrics
        results = self._calculate_metrics(x, y, params)
        
        if return_trajectory:
            results['trajectory'] = trajectory
            
        return results
    
    def _bsb_evolution(self, x: torch.Tensor, y: torch.Tensor, params: Dict[str, torch.Tensor], 
                       p: torch.Tensor, trajectory: Optional[list]) -> Tuple[torch.Tensor, torch.Tensor]:
        """BSB algorithm evolution with differentiable operations."""
        dt, xi, delta = params['dt'], params['xi'], params['delta']
        
        for i in range(self.n_iter):
            # BSB update equations
            if self.h is None:
                y_update = (-(delta - p[i]) * x + xi * torch.sparse.mm(self.J, x)) * dt
            else:
                y_update = (-(delta - p[i]) * x + xi * (torch.sparse.mm(self.J, x) + self.h)) * dt
            
            y = y + y_update
            x = x + dt * y * delta
            
            # Soft clipping instead of hard clipping
            x = self.soft_clip(x, threshold=1.0)
            
            # Reset momentum for clipped variables (soft version)
            clip_mask = (torch.abs(x) > 0.99).float()
            y = y * (1 - clip_mask)
            
            if trajectory is not None:
                trajectory.append({'x': x.clone(), 'y': y.clone(), 'step': i})
                
        return x, y
    
    def _asb_evolution(self, x: torch.Tensor, y: torch.Tensor, params: Dict[str, torch.Tensor], 
                       p: torch.Tensor, trajectory: Optional[list]) -> Tuple[torch.Tensor, torch.Tensor]:
        """ASB algorithm evolution with differentiable operations."""
        dt, xi, delta, M = params['dt'], params['xi'], params['delta'], params['M']
        K = 1.0  # Kerr coefficient
        dm = dt / M
        
        for i in range(self.n_iter):
            # M sub-steps without mean-field
            for _ in range(int(M.item()) if not self.learnable_params.get('M', False) else 1):
                if self.learnable_params.get('M', False):
                    # Weighted update for differentiable M
                    x_update = dm * y * delta * M / int(M.item() + 0.5)
                    y_update = (K * x**3 + (delta - p[i]) * x) * dm * M / int(M.item() + 0.5)
                else:
                    x_update = dm * y * delta
                    y_update = (K * x**3 + (delta - p[i]) * x) * dm
                
                x = x + x_update
                y = y - y_update
            
            # Mean-field update
            if self.h is None:
                y = y + xi * dt * torch.sparse.mm(self.J, x)
            else:
                y = y + xi * dt * (torch.sparse.mm(self.J, x) + self.h)
            
            if trajectory is not None:
                trajectory.append({'x': x.clone(), 'y': y.clone(), 'step': i})
                
        return x, y
    
    def _dsb_evolution(self, x: torch.Tensor, y: torch.Tensor, params: Dict[str, torch.Tensor], 
                       p: torch.Tensor, trajectory: Optional[list]) -> Tuple[torch.Tensor, torch.Tensor]:
        """DSB algorithm evolution with differentiable operations."""
        dt, xi, delta = params['dt'], params['xi'], params['delta']
        
        for i in range(self.n_iter):
            # Use soft sign instead of hard sign
            x_sign = self.soft_sign(x)
            
            if self.h is None:
                y_update = (-(delta - p[i]) * x + xi * torch.sparse.mm(self.J, x_sign)) * dt
            else:
                y_update = (-(delta - p[i]) * x + xi * (torch.sparse.mm(self.J, x_sign) + self.h)) * dt
            
            y = y + y_update
            x = x + dt * y * delta
            
            # Soft clipping
            x = self.soft_clip(x, threshold=1.0)
            
            # Reset momentum for clipped variables
            clip_mask = (torch.abs(x) > 0.99).float()
            y = y * (1 - clip_mask)
            
            if trajectory is not None:
                trajectory.append({'x': x.clone(), 'y': y.clone(), 'step': i})
                
        return x, y
    
    def _calculate_metrics(self, x: torch.Tensor, y: torch.Tensor, params: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Calculate optimization metrics."""
        # Use soft sign for final configuration
        x_final = self.soft_sign(x, temperature=10.0)  # High temperature for near-binary
        
        # Calculate cut value (for MaxCut problems)
        cut_value = 0.25 * torch.sum(torch.sparse.mm(self.J, x_final) * x_final, dim=0) - 0.25 * self.J.sum()
        
        # Calculate energy
        if self.h is None:
            energy = -0.5 * torch.sum(torch.sparse.mm(self.J, x_final) * x_final, dim=0)
        else:
            energy = -0.5 * torch.sum(torch.sparse.mm(self.J, x_final) * x_final, dim=0) - self.h.T @ x_final
        
        return {
            'x_final': x,
            'x_binary': x_final,
            'cut_value': cut_value,
            'energy': energy,
            'max_cut': torch.max(cut_value),
            'mean_cut': torch.mean(cut_value),
            'parameters': params
        }
