{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["In this jupyter notebook, we want to demonstrate using manipulated gradients to boost the performance of FEM.\n", "The example we use here will be MaxCut problem in Gset 1. The best known maximum cut in this instance is 11624.\n", "\n", "Firstly load all the required environments, be careful that the version of pytorch should be at least 2.0."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../')\n", "from FEM import FEM\n", "import torch\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then set up some hyperparameters for usage."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["num_trials = 500\n", "num_steps = 1000\n", "dev = 'cuda' # if you do not have gpu in your computing devices, then choose 'cpu' here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the first case, we use auto-differentiation version of FEM solver with `manual_grad` argument set to be `False`.\n", "The time here is based on execution of codes in a NVIDIA A100 80G GPU.\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["maxcut instance with auto-differentiation, cut value 11559, consume 1.6089 seconds.\n"]}], "source": ["case_maxcut = FEM.from_file(\n", "    'maxcut', 'instances/G1.txt', index_start=1\n", ")\n", "case_maxcut.set_up_solver(\n", "    num_trials, num_steps, manual_grad=False, betamin=0.001, betamax=0.5, \n", "    learning_rate=0.1, optimizer='rmsprop', dev=dev\n", ")\n", "torch.cuda.synchronize(dev)\n", "t0 = time.perf_counter()\n", "config, result = case_maxcut.solve()\n", "torch.cuda.synchronize(dev)\n", "t1 = time.perf_counter()\n", "optimal_inds = torch.argwhere(result==result.max()).reshape(-1)\n", "print(f'maxcut instance with auto-differentiation, cut value {result.max():.0f}, consume {t1-t0:.4f} seconds.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the second case, we use explicit gradient version of FEM solver with `manual_grad` argument set to be `True`.\n", "The explicit gradient means we explicitly write the gradient form of parameters without using the auto-differentiation.\n", "We can see that the running time is much lower than the previous one and the cut value is slightly better which may cause from the numerical error in the auto-differentiation."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["maxcut instance with explicit gradients, cut value 11617, consume 0.4201 seconds.\n"]}], "source": ["case_maxcut = FEM.from_file(\n", "    'maxcut', 'instances/G1.txt', index_start=1\n", ")\n", "case_maxcut.set_up_solver(\n", "    num_trials, num_steps, manual_grad=True, betamin=0.001, betamax=0.5, \n", "    learning_rate=0.1, optimizer='rmsprop', dev=dev\n", ")\n", "torch.cuda.synchronize(dev)\n", "t0 = time.perf_counter()\n", "config, result = case_maxcut.solve()\n", "torch.cuda.synchronize(dev)\n", "t1 = time.perf_counter()\n", "optimal_inds = torch.argwhere(result==result.max()).reshape(-1)\n", "print(f'maxcut instance with explicit gradients, cut value {result.max():.0f}, consume {t1-t0:.4f} seconds.')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For the final case, we use manipulated gradient version of FEM solver with `manual_grad` argument and `discretization` argument set to be `True`.\n", "The `discretization` argument will change the marginal `p` in energy gradient into `p.round()`, leads to the discretization of the marginal into binary configurations.\n", "This manipulated gradient results in the optimal cut value (11624) found with FEM solver."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["maxcut instance with manipulated gradients, cut value 11624, consume 0.4248 seconds.\n"]}], "source": ["case_maxcut = FEM.from_file(\n", "    'maxcut', 'instances/G1.txt', index_start=1, discretization=True\n", ")\n", "case_maxcut.set_up_solver(\n", "    num_trials, num_steps, manual_grad=True, betamin=0.001, betamax=0.5, \n", "    learning_rate=0.1, optimizer='rmsprop', dev=dev\n", ")\n", "torch.cuda.synchronize(dev)\n", "t0 = time.perf_counter()\n", "config, result = case_maxcut.solve()\n", "torch.cuda.synchronize(dev)\n", "t1 = time.perf_counter()\n", "optimal_inds = torch.argwhere(result==result.max()).reshape(-1)\n", "print(f'maxcut instance with manipulated gradients, cut value {result.max():.0f}, consume {t1-t0:.4f} seconds.')"]}], "metadata": {"kernelspec": {"display_name": "torch2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}