{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Firstly load all the required environments, be careful that the version of pytorch should be at least 2.0"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../')\n", "from FEM import FEM\n", "import torch"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then set up some hyperparameters that will be used in all problem instances."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["num_trials = 500\n", "num_steps = 1000\n", "dev = 'cuda' # if you do not have gpu in your computing devices, then choose 'cpu' here"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The first problem instance will be Maximum Cut (MaxCut) on Gset 1, the best known cut will be 11624."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["maxcut test instance, optimal value 11624.0\n"]}], "source": ["case_maxcut = FEM.from_file(\n", "    'maxcut', 'instances/G1.txt', index_start=1, discretization=True\n", ")\n", "case_maxcut.set_up_solver(\n", "    num_trials, num_steps, manual_grad=True, betamin=0.001, betamax=0.5, \n", "    learning_rate=0.1, optimizer='rmsprop', dev=dev\n", ")\n", "config, result = case_maxcut.solve()\n", "optimal_inds = torch.argwhere(result==result.max()).reshape(-1)\n", "print(f'maxcut test instance, optimal value {result.max()}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that here the optimal value can be easily obtained by our FEM solver.The next problem instance will be balance minimum cut (bMinCut). The goal will be partition the graph into balanced piece and make sure the cut between them are as small as possible. Here we choose the well known karate club graph, and divide it into three pieces in order to demonstrate the ability of FEM to solve optimization problems with multiple state variables."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["bmincut test instance, optimal value 20.0\n"]}], "source": ["case_bmincut = FEM.from_file(\n", "    'bmincut', 'instances/karate.txt', index_start=1\n", ")\n", "case_bmincut.set_up_solver(num_trials, num_steps, dev=dev, q=3)\n", "config, result = case_bmincut.solve()\n", "optimal_inds = torch.argwhere(result==result.min()).reshape(-1)\n", "print(f'bmincut test instance, optimal value {result.min()}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next problem instance is the famous vertex cover problem, the test graph is a small graph with 5 vertices and 6 edges. This graph is small enough thus we can explicitly show the optimal configuration here."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["vertexcover test instance, optimal value 3.0\n", "optimal configs are\n", "[0. 1. 1. 0. 1.]\n", "[0. 1. 1. 1. 0.]\n", "[1. 0. 0. 1. 1.]\n", "[1. 0. 1. 1. 0.]\n"]}], "source": ["case_vertexcover = FEM.from_file(\n", "    'vertexcover', 'instances/vertexcover.txt', index_start=0\n", ")\n", "case_vertexcover.set_up_solver(\n", "    num_trials, num_steps, betamin=10, betamax=30, h_factor=1, dev=dev,\n", "    learning_rate=0.01, anneal='exp'\n", ")\n", "config, result =case_vertexcover.solve()\n", "optimal_inds = torch.argwhere(result==result.min()).reshape(-1)\n", "print(f'vertexcover test instance, optimal value {result.min()}')\n", "optimal_configs = torch.unique(config[optimal_inds], dim=0)\n", "print('optimal configs are')\n", "for conf in optimal_configs:\n", "    print(conf.cpu().detach().numpy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The final build-in problem instances will be one with multi-variable interactions, which is unique from all problem instances above. It is the maximum k-satisfactory (Max-kSAT), which is given all the variable states, what is the maximum number of satisfied clauses. Here the test instance is from [Max-SAT 2016](http://www.maxsat.udl.cat/16/index.html)."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["maxksat test instance, optimal value 47.0\n"]}], "source": ["case_maxksat = FEM.from_file(\n", "    'maxksat', 'instances/s3v70c1000-1.cnf'\n", ")\n", "case_maxksat.set_up_solver(\n", "    num_trials, num_steps, manual_grad=True, h_factor=0.3, anneal='lin',\n", "    betamin=0.01, betamax=30, learning_rate=1.1, sparse=True, dev=dev,\n", ")\n", "config, result = case_maxksat.solve()\n", "optimal_inds = torch.argwhere(result==result.min()).reshape(-1)\n", "print(f'maxksat test instance, optimal value {result.min()}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here FEM solver can find the optimal value as the best solver in the competition."]}], "metadata": {"kernelspec": {"display_name": "torch2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}