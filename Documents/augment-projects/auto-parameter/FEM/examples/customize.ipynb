{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Here we introduce how to customize your own function to use FEM solver to solve the optimization problems you want. The following example is using customize function to solve maximum independent set (MIS) problem using FEM solver. Firstly load all the environments and read the graph instance."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append('../')\n", "from FEM import FEM, read_graph\n", "import torch\n", "\n", "num_nodes, num_interactions, couplings = read_graph(\n", "    'instances/mis.txt'\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then according to this [paper](https://arxiv.org/pdf/1801.08653.pdf), the MIS problem can be formulated as QUBO type and the target function will be \n", "$$\n", "E = -\\sum_{i}x_i + 2\\sum_{(i,j)}x_ix_j\n", "$$\n", "we then encode this target function into the customize expectation function and write the corresponding inference function (which is used to infer the value of target function from the marginal probabilities)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def customize_expected_func(J, p):\n", "    couplings = -torch.eye(<PERSON><PERSON>shape[0], dtype=J.dtype, device=J.device) + 2 * J\n", "    return torch.bmm(\n", "        (p @ couplings).reshape(-1, 1, J.shape[1]),\n", "        p.reshape(-1, p.shape[1], 1)\n", "    ).reshape(-1)\n", "\n", "def customize_infer_func(J, p):\n", "    config = p.round()\n", "    return config, customize_expected_func(J, config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With all prepared, we can use the FEM solver to solve this problem. The method will be choose the `prolem_type` argument to be 'customize' and pass the customized functions into the solver."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["customize (maximum independent set) test instance, optimal value -4.0\n", "optimal configs are\n", "[0. 1. 1. 0. 0. 1. 1. 0.]\n", "[1. 0. 0. 1. 1. 0. 0. 1.]\n"]}], "source": ["num_trials = 100\n", "num_steps = 1000\n", "dev = 'cuda'\n", "case_customize = FEM.from_couplings(\n", "    'customize', num_nodes, num_interactions, couplings,\n", "    customize_expected_func=customize_expected_func,\n", "    customize_infer_func=customize_infer_func\n", ")\n", "case_customize.set_up_solver(num_trials, num_steps, dev=dev)\n", "case_customize.solver.binary = True\n", "config, result = case_customize.solve()\n", "optimal_inds = torch.argwhere(result==result.min()).reshape(-1)\n", "print(f'customize (maximum independent set) test instance, optimal value {result.min()}')\n", "optimal_configs = torch.unique(config[optimal_inds], dim=0)\n", "print('optimal configs are')\n", "for conf in optimal_configs:\n", "    print(conf.cpu().detach().numpy())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The graph example is from the [wikipedia](https://en.wikipedia.org/wiki/Maximal_independent_set) and looks like ![The graph of the cube has six different maximal independent sets (two of them are maximum), shown as the red vertices.](https://upload.wikimedia.org/wikipedia/commons/thumb/b/b6/Cube-maximal-independence.svg/600px-Cube-maximal-independence.svg.png)\n", "\n", "we can see that the optimal configuration found by the FEM solver is the two maximum independent set lie in the middle."]}], "metadata": {"kernelspec": {"display_name": "torch2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}