{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## FEM for balanced min cut benchmarking"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from fem4bmincut import *"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Real-world instances"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from add20, 2395 vertices, 7462 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- add20: target group: 2 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 596.00, max 1274.00, mean 737.96, std 82.37  \tTime: 24.1928410 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  596.0  <<<<============ Cut of FEM. \n", "max_group_size: 1198.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 596.00, max 1296.00, mean 741.01, std 83.08  \tTime: 24.1592690 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  596.0  <<<<============ Cut of FEM. \n", "max_group_size: 1198.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 596.00, max 1309.00, mean 737.16, std 83.03  \tTime: 24.1636167 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  596.0  <<<<============ Cut of FEM. \n", "max_group_size: 1198.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 596.00, max 1325.00, mean 740.09, std 86.42  \tTime: 24.1494603 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  596.0  <<<<============ Cut of FEM. \n", "max_group_size: 1198.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 599.00, max 1293.00, mean 739.67, std 82.43  \tTime: 24.1542240 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  599.0  <<<<============ Cut of FEM. \n", "max_group_size: 1198.0.\n", "best_cut: 596.0\n", "best_cut_seed: [13]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from add20, 2395 vertices, 7462 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- add20: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 1152.00, max 1339.00, mean 1229.73, std 40.24  \tTime: 45.3364204 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1185.0  <<<<============ Cut of FEM. \n", "max_group_size: 599.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 1161.00, max 1419.00, mean 1229.90, std 41.82  \tTime: 45.3453665 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1166.0  <<<<============ Cut of FEM. \n", "max_group_size: 599.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 1152.00, max 1409.00, mean 1229.20, std 40.35  \tTime: 45.3337157 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1192.0  <<<<============ Cut of FEM. \n", "max_group_size: 599.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 1151.00, max 1410.00, mean 1228.46, std 39.86  \tTime: 45.3449973 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1169.0  <<<<============ Cut of FEM. \n", "max_group_size: 599.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 1153.00, max 1407.00, mean 1229.11, std 40.49  \tTime: 45.3327052 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1177.0  <<<<============ Cut of FEM. \n", "max_group_size: 599.0.\n", "best_cut: 1166.0\n", "best_cut_seed: [5]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from add20, 2395 vertices, 7462 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- add20: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 1695.00, max 1876.00, mean 1749.67, std 28.04  \tTime: 88.2048587 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1695.0  <<<<============ Cut of FEM. \n", "max_group_size: 300.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 1696.00, max 1880.00, mean 1749.36, std 26.97  \tTime: 88.1501149 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1696.0  <<<<============ Cut of FEM. \n", "max_group_size: 300.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 1697.00, max 1897.00, mean 1749.92, std 26.75  \tTime: 88.1517186 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1699.0  <<<<============ Cut of FEM. \n", "max_group_size: 300.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 1694.00, max 1906.00, mean 1748.98, std 27.67  \tTime: 88.1981639 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1695.0  <<<<============ Cut of FEM. \n", "max_group_size: 300.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 1695.00, max 1876.00, mean 1749.67, std 28.04  \tTime: 88.1650144 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1695.0  <<<<============ Cut of FEM. \n", "max_group_size: 300.0.\n", "best_cut: 1695.0\n", "best_cut_seed: [21]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from add20, 2395 vertices, 7462 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- add20: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 2064.00, max 2373.00, mean 2108.81, std 15.73  \tTime: 176.4064708 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2072.0  <<<<============ Cut of FEM. \n", "max_group_size: 150.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 2070.00, max 2165.00, mean 2108.72, std 14.57  \tTime: 176.4551237 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2072.0  <<<<============ Cut of FEM. \n", "max_group_size: 150.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 2065.00, max 2362.00, mean 2108.89, std 15.85  \tTime: 176.3663427 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2065.0  <<<<============ Cut of FEM. \n", "max_group_size: 150.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 2061.00, max 2170.00, mean 2107.86, std 14.47  \tTime: 176.3848115 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2075.0  <<<<============ Cut of FEM. \n", "max_group_size: 150.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 2065.00, max 2362.00, mean 2108.89, std 15.85  \tTime: 176.3431085 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2065.0  <<<<============ Cut of FEM. \n", "max_group_size: 150.0.\n", "best_cut: 2065.0\n", "best_cut_seed: [36]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from add20, 2395 vertices, 7462 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- add20: target group: 32 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 2396.00, max 2492.00, mean 2441.94, std 15.16  \tTime: 350.7620603 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2396.0  <<<<============ Cut of FEM. \n", "max_group_size: 75.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 2400.00, max 2493.00, mean 2442.24, std 15.02  \tTime: 350.9154109 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2400.0  <<<<============ Cut of FEM. \n", "max_group_size: 75.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 2400.00, max 2493.00, mean 2442.24, std 15.02  \tTime: 350.8074034 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2400.0  <<<<============ Cut of FEM. \n", "max_group_size: 75.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 2402.00, max 2500.00, mean 2442.82, std 14.94  \tTime: 350.7627210 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2402.0  <<<<============ Cut of FEM. \n", "max_group_size: 75.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 2392.00, max 2505.00, mean 2443.43, std 15.38  \tTime: 350.7045444 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  2392.0  <<<<============ Cut of FEM. \n", "max_group_size: 75.0.\n", "best_cut: 2392.0\n", "best_cut_seed: [34]\n"]}], "source": ["instance = 'add20'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [2,4,8,16,32]: # q = 2, 4, 8, 16, 32\n", "    main(instance, q, device, trials)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from data, 2851 vertices, 15093 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- data: target group: 2 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FEM:\tmin 189.00, max 342.00, mean 214.89, std 13.43  \tTime: 28.4232503 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  189.0  <<<<============ Cut of FEM. \n", "max_group_size: 1426.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 189.00, max 298.00, mean 214.26, std 12.52  \tTime: 28.4025300 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  189.0  <<<<============ Cut of FEM. \n", "max_group_size: 1426.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 189.00, max 317.00, mean 214.45, std 12.57  \tTime: 28.4298399 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  189.0  <<<<============ Cut of FEM. \n", "max_group_size: 1426.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 189.00, max 280.00, mean 214.17, std 12.75  \tTime: 28.4186718 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  189.0  <<<<============ Cut of FEM. \n", "max_group_size: 1426.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 189.00, max 298.00, mean 214.26, std 12.52  \tTime: 28.4468913 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  189.0  <<<<============ Cut of FEM. \n", "max_group_size: 1426.0.\n", "best_cut: 189.0\n", "best_cut_seed: [20]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from data, 2851 vertices, 15093 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- data: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 382.00, max 534.00, mean 432.32, std 20.39  \tTime: 55.5978371 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  382.0  <<<<============ Cut of FEM. \n", "max_group_size: 713.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 382.00, max 540.00, mean 432.40, std 20.54  \tTime: 55.5581200 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  382.0  <<<<============ Cut of FEM. \n", "max_group_size: 713.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 382.00, max 537.00, mean 431.99, std 20.92  \tTime: 55.6039756 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  382.0  <<<<============ Cut of FEM. \n", "max_group_size: 713.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 382.00, max 595.00, mean 432.66, std 21.03  \tTime: 55.6077555 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  382.0  <<<<============ Cut of FEM. \n", "max_group_size: 713.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 382.00, max 540.00, mean 431.97, std 20.74  \tTime: 55.5824422 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  382.0  <<<<============ Cut of FEM. \n", "max_group_size: 713.0.\n", "best_cut: 382.0\n", "best_cut_seed: [12]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from data, 2851 vertices, 15093 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- data: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 670.00, max 838.00, mean 732.46, std 23.11  \tTime: 106.2662524 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  675.0  <<<<============ Cut of FEM. \n", "max_group_size: 357.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 671.00, max 848.00, mean 731.28, std 22.09  \tTime: 106.3140043 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  673.0  <<<<============ Cut of FEM. \n", "max_group_size: 357.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 670.00, max 837.00, mean 730.52, std 21.33  \tTime: 106.2520675 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  674.0  <<<<============ Cut of FEM. \n", "max_group_size: 357.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 670.00, max 846.00, mean 731.68, std 22.83  \tTime: 106.3343671 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  675.0  <<<<============ Cut of FEM. \n", "max_group_size: 357.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 669.00, max 828.00, mean 732.00, std 23.01  \tTime: 106.3071794 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  673.0  <<<<============ Cut of FEM. \n", "max_group_size: 357.0.\n", "best_cut: 673.0\n", "best_cut_seed: [5]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from data, 2851 vertices, 15093 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- data: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 1132.00, max 1310.00, mean 1180.80, std 20.48  \tTime: 212.3904229 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1132.0  <<<<============ Cut of FEM. \n", "max_group_size: 179.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 1131.00, max 1259.00, mean 1181.04, std 20.44  \tTime: 212.3543218 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1131.0  <<<<============ Cut of FEM. \n", "max_group_size: 179.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 1134.00, max 1271.00, mean 1181.01, std 20.79  \tTime: 212.1350080 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1134.0  <<<<============ Cut of FEM. \n", "max_group_size: 179.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 1135.00, max 1267.00, mean 1180.67, std 20.38  \tTime: 212.2172516 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1135.0  <<<<============ Cut of FEM. \n", "max_group_size: 179.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 1136.00, max 1262.00, mean 1179.52, std 19.71  \tTime: 212.0640210 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1136.0  <<<<============ Cut of FEM. \n", "max_group_size: 179.0.\n", "best_cut: 1131.0\n", "best_cut_seed: [8]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from data, 2851 vertices, 15093 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- data: target group: 32 ---------------------------------------------\n", "\n", "trial = 0, device = cuda\n", "FEM:\tmin 1811.00, max 1953.00, mean 1845.47, std 18.44  \tTime: 505.3814741 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1819.0  <<<<============ Cut of FEM. \n", "max_group_size: 90.0.\n", "\n", "trial = 1, device = cuda\n", "FEM:\tmin 1812.00, max 1965.00, mean 1845.86, std 18.66  \tTime: 505.0283390 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1819.0  <<<<============ Cut of FEM. \n", "max_group_size: 90.0.\n", "\n", "trial = 2, device = cuda\n", "FEM:\tmin 1812.00, max 1943.00, mean 1845.24, std 18.01  \tTime: 505.0865171 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1819.0  <<<<============ Cut of FEM. \n", "max_group_size: 90.0.\n", "\n", "trial = 3, device = cuda\n", "FEM:\tmin 1812.00, max 1951.00, mean 1845.62, std 19.16  \tTime: 504.9934123 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1818.0  <<<<============ Cut of FEM. \n", "max_group_size: 90.0.\n", "\n", "trial = 4, device = cuda\n", "FEM:\tmin 1812.00, max 1971.00, mean 1844.19, std 18.01  \tTime: 505.0537058 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1819.0  <<<<============ Cut of FEM. \n", "max_group_size: 90.0.\n", "best_cut: 1818.0\n", "best_cut_seed: [24]\n"]}], "source": ["instance = 'data'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [2,4,8,16,32]: # q = 2, 4, 8, 16, 32 \n", "    main(instance, q, device, trials)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from 3elt, 4720 vertices, 13722 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- 3elt: target group: 2 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 90.00, max 765.00, mean 232.54, std 84.90  \tTime: 44.8114555 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  90.0  <<<<============ Cut of FEM. \n", "max_group_size: 2360.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 90.00, max 856.00, mean 231.18, std 84.00  \tTime: 44.8112865 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  90.0  <<<<============ Cut of FEM. \n", "max_group_size: 2360.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 90.00, max 784.00, mean 232.41, std 84.83  \tTime: 44.8145024 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  90.0  <<<<============ Cut of FEM. \n", "max_group_size: 2360.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 90.00, max 799.00, mean 231.11, std 83.27  \tTime: 44.8618532 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  90.0  <<<<============ Cut of FEM. \n", "max_group_size: 2360.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 90.00, max 732.00, mean 231.84, std 85.70  \tTime: 44.8327156 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  90.0  <<<<============ Cut of FEM. \n", "max_group_size: 2360.0.\n", "best_cut: 90.0\n", "best_cut_seed: [48]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from 3elt, 4720 vertices, 13722 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- 3elt: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 202.00, max 477.00, mean 285.61, std 39.91  \tTime: 87.9534703 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  202.0  <<<<============ Cut of FEM. \n", "max_group_size: 1180.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 201.00, max 563.00, mean 285.00, std 41.46  \tTime: 87.9638384 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  201.0  <<<<============ Cut of FEM. \n", "max_group_size: 1180.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 201.00, max 481.00, mean 285.55, std 39.99  \tTime: 87.9355944 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  201.0  <<<<============ Cut of FEM. \n", "max_group_size: 1180.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 202.00, max 668.00, mean 285.71, std 41.73  \tTime: 87.9450027 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  202.0  <<<<============ Cut of FEM. \n", "max_group_size: 1180.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 202.00, max 514.00, mean 283.49, std 40.00  \tTime: 87.9756251 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  202.0  <<<<============ Cut of FEM. \n", "max_group_size: 1180.0.\n", "best_cut: 201.0\n", "best_cut_seed: [39]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from 3elt, 4720 vertices, 13722 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- 3elt: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 345.00, max 476.00, mean 382.31, std 24.26  \tTime: 172.5143269 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  346.0  <<<<============ Cut of FEM. \n", "max_group_size: 590.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 346.00, max 473.00, mean 383.84, std 24.04  \tTime: 172.5587760 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  346.0  <<<<============ Cut of FEM. \n", "max_group_size: 590.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 346.00, max 477.00, mean 382.24, std 23.39  \tTime: 172.5769110 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  346.0  <<<<============ Cut of FEM. \n", "max_group_size: 590.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 346.00, max 470.00, mean 382.04, std 23.87  \tTime: 172.4750928 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  346.0  <<<<============ Cut of FEM. \n", "max_group_size: 590.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 346.00, max 481.00, mean 382.21, std 23.53  \tTime: 172.5095874 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  346.0  <<<<============ Cut of FEM. \n", "max_group_size: 590.0.\n", "best_cut: 346.0\n", "best_cut_seed: [27]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from 3elt, 4720 vertices, 13722 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- 3elt: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 572.00, max 690.00, mean 606.81, std 19.58  \tTime: 346.2306864 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  574.0  <<<<============ Cut of FEM. \n", "max_group_size: 295.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 572.00, max 689.00, mean 607.09, std 19.38  \tTime: 346.3154833 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  574.0  <<<<============ Cut of FEM. \n", "max_group_size: 295.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 573.00, max 690.00, mean 606.95, std 19.56  \tTime: 346.4202679 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  574.0  <<<<============ Cut of FEM. \n", "max_group_size: 295.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 574.00, max 684.00, mean 606.48, std 19.74  \tTime: 346.4395870 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  574.0  <<<<============ Cut of FEM. \n", "max_group_size: 295.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 572.00, max 683.00, mean 606.74, std 19.91  \tTime: 346.4335298 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  575.0  <<<<============ Cut of FEM. \n", "max_group_size: 295.0.\n", "best_cut: 574.0\n", "best_cut_seed: [6]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from 3elt, 4720 vertices, 13722 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- 3elt: target group: 32 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 965.00, max 1008.00, mean 977.93, std 6.74  \tTime: 826.9926398 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  965.0  <<<<============ Cut of FEM. \n", "max_group_size: 148.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 964.00, max 1013.00, mean 977.75, std 6.70  \tTime: 827.0306895 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  964.0  <<<<============ Cut of FEM. \n", "max_group_size: 148.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 964.00, max 1013.00, mean 978.04, std 6.85  \tTime: 827.1201394 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  964.0  <<<<============ Cut of FEM. \n", "max_group_size: 148.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 964.00, max 1013.00, mean 977.75, std 6.70  \tTime: 827.0763794 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  964.0  <<<<============ Cut of FEM. \n", "max_group_size: 148.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 964.00, max 1013.00, mean 977.75, std 6.70  \tTime: 827.0001777 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  964.0  <<<<============ Cut of FEM. \n", "max_group_size: 148.0.\n", "best_cut: 964.0\n", "best_cut_seed: [32]\n"]}], "source": ["instance = '3elt'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [2, 4, 8, 16, 32]:  # q = 2, 4, 8, 16, 32\n", "    main(instance, q, device, trials)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from bcsstk33, 8738 vertices, 291583 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- bcsstk33: target group: 2 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FEM:\tmin 10171.00, max 26345.00, mean 13145.99, std 2730.16  \tTime: 100.8888498 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10171.0  <<<<============ Cut of FEM. \n", "max_group_size: 4369.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 10171.00, max 30510.00, mean 13089.88, std 2637.78  \tTime: 100.7652705 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10171.0  <<<<============ Cut of FEM. \n", "max_group_size: 4369.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 10171.00, max 27092.00, mean 13187.31, std 2699.07  \tTime: 100.8759413 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10171.0  <<<<============ Cut of FEM. \n", "max_group_size: 4369.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 10171.00, max 26089.00, mean 13080.47, std 2677.78  \tTime: 100.9647880 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10171.0  <<<<============ Cut of FEM. \n", "max_group_size: 4369.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 10171.00, max 26083.00, mean 12957.99, std 2519.07  \tTime: 100.8929886 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10171.0  <<<<============ Cut of FEM. \n", "max_group_size: 4369.0.\n", "best_cut: 10171.0\n", "best_cut_seed: [20]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from bcsstk33, 8738 vertices, 291583 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- bcsstk33: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 21719.00, max 35544.00, mean 24160.12, std 2037.89  \tTime: 199.5417235 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  21719.0  <<<<============ Cut of FEM. \n", "max_group_size: 2185.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 21718.00, max 34568.00, mean 24193.65, std 1961.37  \tTime: 199.5717190 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  21718.0  <<<<============ Cut of FEM. \n", "max_group_size: 2185.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 21718.00, max 36906.00, mean 24122.52, std 1953.51  \tTime: 199.7664238 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  21718.0  <<<<============ Cut of FEM. \n", "max_group_size: 2185.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 21718.00, max 38012.00, mean 24147.03, std 2010.37  \tTime: 199.7510114 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  21718.0  <<<<============ Cut of FEM. \n", "max_group_size: 2185.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 21718.00, max 35399.00, mean 24167.88, std 1983.16  \tTime: 199.6176128 Secs. for 2000 replicas with 10000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  21718.0  <<<<============ Cut of FEM. \n", "max_group_size: 2185.0.\n", "best_cut: 21718.0\n", "best_cut_seed: [26]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from bcsstk33, 8738 vertices, 291583 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- bcsstk33: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 34436.00, max 40422.00, mean 34830.89, std 836.52  \tTime: 469.7682186 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  34446.0  <<<<============ Cut of FEM. \n", "max_group_size: 1093.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 34436.00, max 40140.00, mean 34839.82, std 877.35  \tTime: 469.6166493 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  34440.0  <<<<============ Cut of FEM. \n", "max_group_size: 1093.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 34436.00, max 41211.00, mean 34836.19, std 882.96  \tTime: 470.0922646 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  34446.0  <<<<============ Cut of FEM. \n", "max_group_size: 1093.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 34436.00, max 42162.00, mean 34828.11, std 841.06  \tTime: 469.6636551 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  34440.0  <<<<============ Cut of FEM. \n", "max_group_size: 1093.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 34436.00, max 40692.00, mean 34841.30, std 858.00  \tTime: 469.7557947 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  34446.0  <<<<============ Cut of FEM. \n", "max_group_size: 1093.0.\n", "best_cut: 34440.0\n", "best_cut_seed: [24]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from bcsstk33, 8738 vertices, 291583 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- bcsstk33: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 54697.00, max 55812.00, mean 54903.78, std 183.22  \tTime: 945.6550782 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  54697.0  <<<<============ Cut of FEM. \n", "max_group_size: 547.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 54697.00, max 55988.00, mean 54896.29, std 178.12  \tTime: 945.7038476 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  54697.0  <<<<============ Cut of FEM. \n", "max_group_size: 547.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 54697.00, max 55872.00, mean 54901.09, std 181.35  \tTime: 946.2223598 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  54697.0  <<<<============ Cut of FEM. \n", "max_group_size: 547.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 54697.00, max 56242.00, mean 54898.28, std 186.76  \tTime: 945.8018874 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  54697.0  <<<<============ Cut of FEM. \n", "max_group_size: 547.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 54697.00, max 56242.00, mean 54898.28, std 186.76  \tTime: 947.2639895 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  54697.0  <<<<============ Cut of FEM. \n", "max_group_size: 547.0.\n", "best_cut: 54697.0\n", "best_cut_seed: [17]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from bcsstk33, 8738 vertices, 291583 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- bcsstk33: target group: 32 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 77503.00, max 78738.00, mean 77878.47, std 209.12  \tTime: 1940.2544417 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  77505.0  <<<<============ Cut of FEM. \n", "max_group_size: 274.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 77506.00, max 78732.00, mean 77886.85, std 214.44  \tTime: 1939.0829742 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  77506.0  <<<<============ Cut of FEM. \n", "max_group_size: 274.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 77503.00, max 78772.00, mean 77889.14, std 216.89  \tTime: 1939.5623009 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  77506.0  <<<<============ Cut of FEM. \n", "max_group_size: 274.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 77503.00, max 78695.00, mean 77880.52, std 209.22  \tTime: 1938.4586463 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  77506.0  <<<<============ Cut of FEM. \n", "max_group_size: 274.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 77506.00, max 78834.00, mean 77885.14, std 214.91  \tTime: 1939.2291502 Secs. for 2000 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  77506.0  <<<<============ Cut of FEM. \n", "max_group_size: 274.0.\n", "best_cut: 77505.0\n", "best_cut_seed: [37]\n"]}], "source": ["instance = 'bcsstk33'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [2,4,8,16,32]: # q = 2, 4, 8, 16, 32\n", "    main(instance, q, device, trials)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Synthetic <PERSON><PERSON><PERSON> random graphs with average connectivity of 5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N1000c5, 1000 vertices, 2479 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N1000c5: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 727.00, max 745.00, mean 734.95, std 3.75  \tTime: 6.3388322 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  727.0  <<<<============ Cut of FEM. \n", "max_group_size: 250.0.\n", "\n", "trial = 1, device = cuda:1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FEM:\tmin 726.00, max 744.00, mean 734.39, std 3.69  \tTime: 6.3360872 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  726.0  <<<<============ Cut of FEM. \n", "max_group_size: 250.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 724.00, max 751.00, mean 734.71, std 4.31  \tTime: 6.3440026 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  724.0  <<<<============ Cut of FEM. \n", "max_group_size: 250.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 725.00, max 744.00, mean 734.77, std 3.64  \tTime: 6.3215596 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  725.0  <<<<============ Cut of FEM. \n", "max_group_size: 250.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 727.00, max 744.00, mean 734.80, std 3.56  \tTime: 6.3166194 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  727.0  <<<<============ Cut of FEM. \n", "max_group_size: 250.0.\n", "best_cut: 724.0\n", "best_cut_seed: [23]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N1000c5, 1000 vertices, 2479 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N1000c5: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 923.00, max 948.00, mean 934.19, std 4.46  \tTime: 6.2992615 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  926.0  <<<<============ Cut of FEM. \n", "max_group_size: 125.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 926.00, max 946.00, mean 934.30, std 4.18  \tTime: 6.2954261 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  926.0  <<<<============ Cut of FEM. \n", "max_group_size: 125.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 925.00, max 945.00, mean 934.29, std 4.31  \tTime: 6.3450459 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  925.0  <<<<============ Cut of FEM. \n", "max_group_size: 125.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 924.00, max 943.00, mean 933.71, std 4.09  \tTime: 6.3254747 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  926.0  <<<<============ Cut of FEM. \n", "max_group_size: 125.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 922.00, max 946.00, mean 934.28, std 4.54  \tTime: 6.3279711 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  922.0  <<<<============ Cut of FEM. \n", "max_group_size: 125.0.\n", "best_cut: 922.0\n", "best_cut_seed: [5]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N1000c5, 1000 vertices, 2479 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N1000c5: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 1086.00, max 1115.00, mean 1098.48, std 5.55  \tTime: 9.4518537 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1086.0  <<<<============ Cut of FEM. \n", "max_group_size: 63.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 1084.00, max 1114.00, mean 1098.62, std 5.17  \tTime: 9.4096039 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1084.0  <<<<============ Cut of FEM. \n", "max_group_size: 63.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 1085.00, max 1113.00, mean 1098.68, std 5.45  \tTime: 9.4209220 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1088.0  <<<<============ Cut of FEM. \n", "max_group_size: 63.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 1084.00, max 1112.00, mean 1098.74, std 4.91  \tTime: 9.3943665 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1087.0  <<<<============ Cut of FEM. \n", "max_group_size: 63.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 1084.00, max 1112.00, mean 1098.74, std 4.91  \tTime: 9.4053081 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1087.0  <<<<============ Cut of FEM. \n", "max_group_size: 63.0.\n", "best_cut: 1084.0\n", "best_cut_seed: [41]\n"]}], "source": ["instance = 'N1000c5'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [4,8,16]:  # q = 4, 8, 16\n", "    main(instance, q, device, trials)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N10000c5, 10000 vertices, 25227 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N10000c5: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["FEM:\tmin 7438.00, max 7521.00, mean 7473.81, std 15.77  \tTime: 24.9154538 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  7438.0  <<<<============ Cut of FEM. \n", "max_group_size: 2500.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 7428.00, max 7514.00, mean 7472.56, std 15.74  \tTime: 24.9068073 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  7428.0  <<<<============ Cut of FEM. \n", "max_group_size: 2500.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 7431.00, max 7506.00, mean 7471.64, std 14.90  \tTime: 24.9062064 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  7431.0  <<<<============ Cut of FEM. \n", "max_group_size: 2500.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 7427.00, max 7513.00, mean 7470.96, std 15.72  \tTime: 24.9448044 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  7427.0  <<<<============ Cut of FEM. \n", "max_group_size: 2500.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 7437.00, max 7521.00, mean 7473.83, std 15.08  \tTime: 24.9165688 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  7437.0  <<<<============ Cut of FEM. \n", "max_group_size: 2500.0.\n", "best_cut: 7427.0\n", "best_cut_seed: [30]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N10000c5, 10000 vertices, 25227 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N10000c5: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 9416.00, max 9550.00, mean 9475.56, std 22.71  \tTime: 45.4532282 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  9416.0  <<<<============ Cut of FEM. \n", "max_group_size: 1250.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 9415.00, max 9628.00, mean 9476.42, std 27.18  \tTime: 45.4775384 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  9415.0  <<<<============ Cut of FEM. \n", "max_group_size: 1250.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 9411.00, max 9556.00, mean 9475.48, std 24.22  \tTime: 45.4386133 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  9411.0  <<<<============ Cut of FEM. \n", "max_group_size: 1250.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 9421.00, max 9599.00, mean 9476.77, std 25.14  \tTime: 45.4634653 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  9421.0  <<<<============ Cut of FEM. \n", "max_group_size: 1250.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 9422.00, max 9562.00, mean 9473.23, std 24.15  \tTime: 45.4689894 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  9422.0  <<<<============ Cut of FEM. \n", "max_group_size: 1250.0.\n", "best_cut: 9411.0\n", "best_cut_seed: [20]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N10000c5, 10000 vertices, 25227 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N10000c5: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 10767.00, max 10852.00, mean 10811.46, std 16.53  \tTime: 89.2917631 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10767.0  <<<<============ Cut of FEM. \n", "max_group_size: 625.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 10762.00, max 10843.00, mean 10809.93, std 16.61  \tTime: 89.2713815 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10762.0  <<<<============ Cut of FEM. \n", "max_group_size: 625.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 10758.00, max 10840.00, mean 10806.00, std 16.17  \tTime: 89.2949750 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10758.0  <<<<============ Cut of FEM. \n", "max_group_size: 625.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 10762.00, max 10843.00, mean 10809.93, std 16.61  \tTime: 89.3024696 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10762.0  <<<<============ Cut of FEM. \n", "max_group_size: 625.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 10765.00, max 10853.00, mean 10811.06, std 15.39  \tTime: 89.2838281 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  10765.0  <<<<============ Cut of FEM. \n", "max_group_size: 625.0.\n", "best_cut: 10758.0\n", "best_cut_seed: [19]\n"]}], "source": ["instance = 'N10000c5'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [4,8,16]: # q = 4, 8, 16\n", "    main(instance, q, device, trials)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N100000c5, 100000 vertices, 250759 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N100000c5: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 73706.00, max 74075.00, mean 73853.77, std 61.22  \tTime: 226.9636981 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  73800.0  <<<<============ Cut of FEM. \n", "max_group_size: 25000.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 73734.00, max 74019.00, mean 73857.24, std 56.50  \tTime: 226.8864384 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  73745.0  <<<<============ Cut of FEM. \n", "max_group_size: 25000.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 73690.00, max 74026.00, mean 73855.80, std 57.61  \tTime: 226.9183060 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  73794.0  <<<<============ Cut of FEM. \n", "max_group_size: 25000.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 73706.00, max 74054.00, mean 73852.09, std 55.67  \tTime: 226.8962402 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  73772.0  <<<<============ Cut of FEM. \n", "max_group_size: 25000.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 73702.00, max 74022.00, mean 73847.95, std 60.83  \tTime: 226.9356918 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  73733.0  <<<<============ Cut of FEM. \n", "max_group_size: 25000.0.\n", "best_cut: 73733.0\n", "best_cut_seed: [34]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N100000c5, 100000 vertices, 250759 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N100000c5: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 92720.00, max 93305.00, mean 92900.33, std 100.67  \tTime: 445.3375093 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  92727.0  <<<<============ Cut of FEM. \n", "max_group_size: 12500.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 92717.00, max 93390.00, mean 92908.54, std 109.11  \tTime: 445.2701602 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  92721.0  <<<<============ Cut of FEM. \n", "max_group_size: 12500.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 92691.00, max 93320.00, mean 92899.16, std 112.47  \tTime: 445.2802437 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  92691.0  <<<<============ Cut of FEM. \n", "max_group_size: 12500.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 92701.00, max 97035.00, mean 92931.20, std 308.66  \tTime: 445.3371640 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  92701.0  <<<<============ Cut of FEM. \n", "max_group_size: 12500.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 92702.00, max 93290.00, mean 92911.15, std 118.13  \tTime: 445.3314462 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  92702.0  <<<<============ Cut of FEM. \n", "max_group_size: 12500.0.\n", "best_cut: 92691.0\n", "best_cut_seed: [2]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N100000c5, 100000 vertices, 250759 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N100000c5: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 108516.00, max 117506.00, mean 110487.66, std 2212.92  \tTime: 893.7392742 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  108628.0  <<<<============ Cut of FEM. \n", "max_group_size: 6250.0.\n", "\n", "trial = 1, device = cuda:1\n", "FEM:\tmin 108551.00, max 116941.00, mean 110598.38, std 2179.64  \tTime: 894.0073389 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  108674.0  <<<<============ Cut of FEM. \n", "max_group_size: 6250.0.\n", "\n", "trial = 2, device = cuda:1\n", "FEM:\tmin 108601.00, max 117493.00, mean 110719.04, std 2323.96  \tTime: 894.2380748 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  108601.0  <<<<============ Cut of FEM. \n", "max_group_size: 6250.0.\n", "\n", "trial = 3, device = cuda:1\n", "FEM:\tmin 108418.00, max 117387.00, mean 110570.48, std 2204.65  \tTime: 894.3762383 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  108418.0  <<<<============ Cut of FEM. \n", "max_group_size: 6250.0.\n", "\n", "trial = 4, device = cuda:1\n", "FEM:\tmin 108545.00, max 116757.00, mean 110520.21, std 2139.65  \tTime: 894.2354494 Secs. for 200 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  108545.0  <<<<============ Cut of FEM. \n", "max_group_size: 6250.0.\n", "best_cut: 108418.0\n", "best_cut_seed: [41]\n"]}], "source": ["instance = 'N100000c5'\n", "device = 'cuda'\n", "trials = 5\n", "for q in [4,8,16]: # q = 4, 8, 16\n", "    main(instance, q, device, trials)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Make sure enough GPU memory for large-scale graphs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N1000000c5, 1000000 vertices, 2503024 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N1000000c5: target group: 4 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 742155.00, max 764294.00, mean 748480.38, std 5341.05  \tTime: 579.2697697 Secs. for 50 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  742155.0  <<<<============ Cut of FEM. \n", "max_group_size: 250000.0.\n", "best_cut: 742155.0\n", "best_cut_seed: [2]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N1000000c5, 1000000 vertices, 2503024 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N1000000c5: target group: 8 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 926482.00, max 1101860.00, mean 941788.06, std 45721.26  \tTime: 1144.1084375 Secs. for 50 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  926604.0  <<<<============ Cut of FEM. \n", "max_group_size: 125000.0.\n", "best_cut: 926604.0\n", "best_cut_seed: [5]\n", ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Processing graph from N1000000c5, 1000000 vertices, 2503024 edges, mode: bmincut <<<<<<<<<<<<<<<<<<<<<<<<<<<<<\n", "\n", "--------------------------------------------- N1000000c5: target group: 16 ---------------------------------------------\n", "\n", "trial = 0, device = cuda:1\n", "FEM:\tmin 1086118.00, max 1139804.00, mean 1097448.75, std 16793.39  \tTime: 2367.3687576 Secs. for 50 replicas with 12000 steps.\n", "\n", "Balanced min-cut value with the ideal group size found by FEM:  1086118.0  <<<<============ Cut of FEM. \n", "max_group_size: 62500.0.\n", "best_cut: 1086118.0\n", "best_cut_seed: [3]\n"]}], "source": ["instance = 'N1000000c5'\n", "device = 'cuda'\n", "trials = 1  \n", "for q in [4,8,16]: # q = 4, 8, 16\n", "    main(instance, q, device, trials)"]}], "metadata": {"kernelspec": {"display_name": "fatcat-szs", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}