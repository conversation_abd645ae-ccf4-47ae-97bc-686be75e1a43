{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## FEM for maxcut （TTS benchmarking）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from fem4maxcut import *"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========= mode: MaxCut =========\n", "instance:  G1\n", "num. of nodes: 800,  seed = 92, device = cuda\n", "\n", "TTS_target: 11624.0\n", "TTT_target: 11507.76\n", "T_com: 15.50 ms\n", "P_s_TTS:  1.0\n", "TTS of FEM: 15.50 ms, TTS of dSB: 33.3 ms\n", "P_s_TTT:  1.0\n", "TTT: 15.50 ms\n", "Params: N_rep: 1000, N_batch: 130, N_step: 1000, scale: 2, C_grad: 1, lr: 0.2, Tmin:8e-05, Tmax: 0.5, alpha: 0.623, weight_decay: 0.02, mom:0.693.\n", "Maximum cut value found:  11624\n", "Best known result of G1 is 11624\n", "FEM: min 11540.00, max 11624.00, mean 11600.14, std 16.68\n"]}], "source": ["dev = 'cuda'  \n", "instance = 'G1'\n", "main(instance,dev)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========= mode: MaxCut =========\n", "instance:  G4\n", "num. of nodes: 800,  seed = 30, device = cuda\n", "\n", "TTS_target: 11646.0\n", "TTT_target: 11529.539999999999\n", "T_com: 12.38 ms\n", "P_s_TTS:  0.992\n", "TTS of FEM: 12.38 ms, TTS of dSB: 34.4 ms\n", "P_s_TTT:  1.0\n", "TTT: 12.38 ms\n", "Params: N_rep: 1000, N_batch: 130, N_step: 800, scale: 2, C_grad: 1, lr: 0.2691, Tmin:0.00089, Tmax: 0.29, alpha: 0.4718, weight_decay: 0.00616, mom:0.7414.\n", "Maximum cut value found:  11646\n", "Best known result of G4 is 11646\n", "FEM: min 11538.00, max 11646.00, mean 11624.47, std 14.79\n"]}], "source": ["dev = 'cuda'  \n", "instance = 'G4'\n", "main(instance,dev)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dev = 'cuda'    \n", "for ins in range(54):  #  all 54 instances\n", "    instance = f'G{ins+1}'\n", "    main(instance,dev)"]}], "metadata": {"kernelspec": {"display_name": "fatcat-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}