# Differentiable SBM: Automatic Parameter Optimization for Simulated Bifurcation Machines

## Overview

This project implements a novel approach to optimize Simulated Bifurcation Machine (SBM) parameters using automatic differentiation, inspired by the Free Energy Machine (FEM) methodology. By making SBM algorithms differentiable, we enable gradient-based parameter optimization that is significantly more efficient than traditional manual tuning or black-box optimization methods.

## Key Innovation

**Problem**: SBM algorithms (BSB, ASB, DSB) require careful manual tuning of parameters like `dt` (time step) and `xi` (frequency), which is time-consuming and problem-specific.

**Solution**: Create differentiable versions of SBM algorithms that allow automatic parameter optimization using gradient descent, similar to how neural networks are trained.

## Architecture

### Core Components

1. **`DifferentiableSBM`**: PyTorch-based implementation of SBM algorithms with learnable parameters
2. **`SBMParameterOptimizer`**: Training framework for automatic parameter optimization
3. **Soft Approximations**: Differentiable replacements for discrete operations
4. **Multi-objective Loss Functions**: Balance performance, stability, and generalization

### Key Technical Innovations

#### Soft Approximations for Differentiability
```python
# Replace hard sign with soft sign
def soft_sign(x, temperature=1.0):
    return torch.tanh(temperature * x)

# Replace hard clipping with soft clipping  
def soft_clip(x, threshold=1.0):
    mask = torch.sigmoid(temperature * (threshold - torch.abs(x)))
    return x * mask + torch.sign(x) * threshold * (1 - mask)
```

#### Learnable Parameter Management
```python
# Bounded parameters using sigmoid
def get_dt(self):
    dt_min, dt_max = 0.01, 5.0
    return torch.sigmoid(self.dt_raw) * (dt_max - dt_min) + dt_min
```

#### Multi-objective Optimization
```python
def loss_function(results):
    # Primary objective: maximize cut value
    primary_loss = -results['max_cut']
    
    # Regularization: parameter stability
    reg_loss = λ₁ * (dt - 1.0)² + λ₂ * (xi - xi_default)²
    
    return primary_loss + reg_loss
```

## Performance Comparison

### Traditional Manual Tuning
- **Method**: Grid search or Optuna/Hyperopt
- **Evaluations**: O(grid_size²) for 2 parameters
- **Time**: Hours to days for thorough search
- **Adaptability**: Problem-specific, requires re-tuning

### Automatic Differentiation Approach
- **Method**: Gradient-based optimization (Adam, RMSprop)
- **Evaluations**: O(epochs) with gradient information
- **Time**: Minutes to hours
- **Adaptability**: Learns problem-specific parameters automatically

### Expected Improvements
- **10-100x faster** parameter optimization
- **Better parameter values** through continuous optimization
- **Automatic adaptation** to different problem types
- **Multi-objective optimization** (performance vs stability)

## Usage Examples

### Quick Start
```python
import torch
from differentiable_sbm import DifferentiableSBM
from sbm_parameter_optimizer import SBMParameterOptimizer

# Create your MaxCut problem
J = torch.randn(50, 50)  # Coupling matrix
J = (J + J.T) / 2        # Make symmetric

# Create differentiable SBM
model = DifferentiableSBM(
    J=J,
    algorithm='BSB',
    learnable_params={'dt': True, 'xi': True}
)

# Optimize parameters automatically
optimizer = SBMParameterOptimizer(model, learning_rate=0.01)
results = optimizer.train(num_epochs=100)

print(f"Optimized parameters: {results['final_parameters']}")
print(f"Final cut value: {results['final_cut_value']}")
```

### Advanced Usage
```python
# Multi-problem validation
validation_problems = [
    (complete_graph_J, None),
    (random_regular_J, None), 
    (erdos_renyi_J, None)
]

# Train with validation
results = optimizer.train(
    num_epochs=200,
    validation_problems=validation_problems,
    verbose=True,
    plot_progress=True
)

# Compare with manual tuning
manual_params = {'dt': 1.0, 'xi': 2.0}
comparison = optimizer.compare_with_manual_tuning(manual_params)
print(f"Improvement: {comparison['improvement']:.3f}")
```

## File Structure

```
auto-parameter/
├── differentiable_sbm.py          # Core differentiable SBM implementation
├── sbm_parameter_optimizer.py     # Parameter optimization framework
├── example_usage.py               # Usage examples and demos
├── test_differentiable_sbm.py     # Test suite
├── IMPLEMENTATION_PLAN.md         # Detailed technical documentation
├── README.md                      # This file
└── qaia_algorithm.py              # Original SBM implementation (for reference)
```

## Supported Algorithms

- **BSB (Ballistic Simulated Bifurcation)**: Continuous dynamics with momentum
- **ASB (Adiabatic Simulated Bifurcation)**: Multi-step evolution with Kerr nonlinearity  
- **DSB (Discrete Simulated Bifurcation)**: Discrete spin updates

## Learnable Parameters

- **`dt`**: Time step size (affects stability and convergence speed)
- **`xi`**: Frequency parameter (controls coupling strength)
- **`delta`**: Detuning frequency (usually fixed)
- **`M`**: Number of sub-steps for ASB (discrete parameter)

## Requirements

```bash
pip install torch numpy scipy networkx matplotlib
```

## Testing

Run the test suite to verify functionality:

```bash
python test_differentiable_sbm.py
```

Expected output:
```
✓ Basic Functionality completed successfully
✓ Gradient Flow completed successfully  
✓ Soft Approximations completed successfully
✓ Parameter Optimization completed successfully

Overall: 4/4 tests passed
🎉 All tests passed! The implementation is working correctly.
```

## Comparison with FEM

| Aspect | SBM (Original) | FEM | Differentiable SBM (This Work) |
|--------|----------------|-----|--------------------------------|
| **Problem Scope** | QUBO/Ising only | General COPs | QUBO/Ising (extensible) |
| **Parameter Optimization** | Manual/Black-box | Automatic (AD) | Automatic (AD) |
| **Dynamics** | Physics-inspired | Statistical physics | Physics-inspired + AD |
| **Scalability** | High | High | High |
| **Generality** | Limited | High | Medium (QUBO focus) |

## Future Extensions

### Immediate Improvements
- **Meta-learning**: Learn parameter initialization strategies
- **Multi-objective Pareto optimization**: Trade-offs between quality and speed
- **Hardware-specific optimization**: GPU/NPU-optimized parameters

### Research Directions
- **Theoretical analysis**: Convergence guarantees for differentiable SBM
- **Better approximations**: Improved soft functions for discrete operations
- **Transfer learning**: Parameter knowledge across problem families

## Citation

If you use this work, please cite:

```bibtex
@misc{differentiable_sbm_2024,
  title={Differentiable Simulated Bifurcation Machines: Automatic Parameter Optimization using Gradient Descent},
  author={[Your Name]},
  year={2024},
  note={Inspired by Free Energy Machine methodology}
}
```

## References

1. **Original SBM Papers**:
   - Goto et al. "Combinatorial optimization by simulating adiabatic bifurcations in nonlinear Hamiltonian systems" Science Advances (2019)
   - Tatsumura et al. "High-performance combinatorial optimization based on classical mechanics" Science Advances (2021)

2. **Free Energy Machine**:
   - Shen et al. "Free-Energy Machine for Combinatorial Optimization" arXiv:2412.09285 (2024)

3. **Automatic Differentiation in Physics**:
   - Cranmer et al. "The frontier of simulation-based inference" PNAS (2020)

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions welcome! Please see CONTRIBUTING.md for guidelines.

---

**Contact**: [Your contact information]

**Last Updated**: December 2024
