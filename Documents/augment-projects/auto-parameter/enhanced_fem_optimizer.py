"""
Enhanced FEM Optimizer and Analytical Utilities

This module provides optimization algorithms and analytical moment computation
utilities for the Enhanced Free Energy Machine.
"""

import torch
import torch.nn as nn
import numpy as np
import math
from typing import Dict, List, Tuple, Optional, Union
from enhanced_fem import EnhancedFEM

class EnhancedFEMOptimizer:
    """Optimizer for Enhanced FEM with temperature annealing."""
    
    def __init__(
        self,
        model: EnhancedFEM,
        optimizer_type: str = 'adam',
        learning_rate: float = 0.01,
        temperature_schedule: str = 'exponential',
        min_temperature: float = 0.01
    ):
        self.model = model
        self.optimizer_type = optimizer_type
        self.learning_rate = learning_rate
        self.temperature_schedule = temperature_schedule
        self.min_temperature = min_temperature
        
        # Setup optimizer
        if optimizer_type == 'adam':
            self.optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
        elif optimizer_type == 'rmsprop':
            self.optimizer = torch.optim.RMSprop(model.parameters(), lr=learning_rate)
        elif optimizer_type == 'sgd':
            self.optimizer = torch.optim.SGD(model.parameters(), lr=learning_rate, momentum=0.9)
        else:
            raise ValueError(f"Unknown optimizer: {optimizer_type}")
        
        # Training history
        self.history = {
            'free_energy': [],
            'expected_energy': [],
            'entropy': [],
            'constraint_violations': [],
            'temperature': [],
            'bounds_penalties': []
        }
    
    def update_temperature(self, epoch: int, max_epochs: int):
        """Update temperature according to annealing schedule."""
        if self.temperature_schedule == 'linear':
            # Linear annealing: T(t) = T_max * (1 - t/T_max) + T_min
            progress = epoch / max_epochs
            new_temp = 1.0 * (1 - progress) + self.min_temperature
        elif self.temperature_schedule == 'exponential':
            # Exponential annealing: T(t) = T_max * (T_min/T_max)^(t/T_max)
            progress = epoch / max_epochs
            new_temp = 1.0 * (self.min_temperature ** progress)
        elif self.temperature_schedule == 'cosine':
            # Cosine annealing
            progress = epoch / max_epochs
            new_temp = self.min_temperature + 0.5 * (1.0 - self.min_temperature) * (1 + math.cos(math.pi * progress))
        else:
            new_temp = 1.0  # Constant temperature
        
        # Update model temperature
        with torch.no_grad():
            self.model.temperature.data = torch.tensor(new_temp, device=self.model.device)
    
    def train(
        self,
        num_epochs: int = 1000,
        verbose: bool = True,
        log_interval: int = 100,
        early_stopping_patience: int = None,
        early_stopping_threshold: float = 1e-6
    ) -> Dict:
        """Train the Enhanced FEM model."""
        
        best_free_energy = float('inf')
        patience_counter = 0
        
        for epoch in range(num_epochs):
            self.optimizer.zero_grad()
            
            # Update temperature
            self.update_temperature(epoch, num_epochs)
            
            # Compute free energy and gradients
            free_energy, info = self.model.free_energy()
            
            # Backward pass
            free_energy.backward()
            
            # Gradient clipping for numerical stability
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # Optimizer step
            self.optimizer.step()
            
            # Record history
            self.history['free_energy'].append(free_energy.item())
            self.history['expected_energy'].append(info['expected_energy'].item())
            self.history['entropy'].append(info['entropy'].item())
            self.history['constraint_violations'].append(info['constraint_violations'].item())
            self.history['bounds_penalties'].append(info['bounds_penalties'].item())
            self.history['temperature'].append(info['temperature'].item())
            
            # Early stopping check
            if early_stopping_patience is not None:
                if free_energy.item() < best_free_energy - early_stopping_threshold:
                    best_free_energy = free_energy.item()
                    patience_counter = 0
                else:
                    patience_counter += 1
                    if patience_counter >= early_stopping_patience:
                        if verbose:
                            print(f"Early stopping at epoch {epoch}")
                        break
            
            # Logging
            if verbose and epoch % log_interval == 0:
                print(f"Epoch {epoch:4d}: "
                      f"F = {free_energy.item():8.4f}, "
                      f"⟨H⟩ = {info['expected_energy'].item():8.4f}, "
                      f"S = {info['entropy'].item():6.4f}, "
                      f"T = {info['temperature'].item():6.4f}, "
                      f"Violations = {info['constraint_violations'].item():6.4f}")
        
        # Get final solution
        solution = self.model.get_solution()
        
        return {
            'solution': solution,
            'final_free_energy': self.history['free_energy'][-1],
            'history': self.history,
            'converged': patience_counter < early_stopping_patience if early_stopping_patience else True
        }

class AnalyticalMoments:
    """Utility class for analytical computation of polynomial moments."""
    
    @staticmethod
    def gaussian_moments(mu: float, sigma2: float, max_order: int = 4) -> Dict[int, float]:
        """
        Compute analytical moments of Gaussian distribution N(μ, σ²).
        
        Returns moments ⟨x^k⟩ for k = 1, 2, ..., max_order
        
        Formulas:
        ⟨x⟩ = μ
        ⟨x²⟩ = μ² + σ²
        ⟨x³⟩ = μ³ + 3μσ²
        ⟨x⁴⟩ = μ⁴ + 6μ²σ² + 3σ⁴
        ⟨x⁵⟩ = μ⁵ + 10μ³σ² + 15μσ⁴
        ⟨x⁶⟩ = μ⁶ + 15μ⁴σ² + 45μ²σ⁴ + 15σ⁶
        """
        moments = {}
        
        # First moment: ⟨x⟩ = μ
        moments[1] = mu
        
        # Second moment: ⟨x²⟩ = μ² + σ²
        moments[2] = mu**2 + sigma2
        
        if max_order >= 3:
            # Third moment: ⟨x³⟩ = μ³ + 3μσ²
            moments[3] = mu**3 + 3*mu*sigma2
        
        if max_order >= 4:
            # Fourth moment: ⟨x⁴⟩ = μ⁴ + 6μ²σ² + 3σ⁴
            moments[4] = mu**4 + 6*mu**2*sigma2 + 3*sigma2**2
        
        if max_order >= 5:
            # Fifth moment: ⟨x⁵⟩ = μ⁵ + 10μ³σ² + 15μσ⁴
            moments[5] = mu**5 + 10*mu**3*sigma2 + 15*mu*sigma2**2
        
        if max_order >= 6:
            # Sixth moment: ⟨x⁶⟩ = μ⁶ + 15μ⁴σ² + 45μ²σ⁴ + 15σ⁶
            moments[6] = mu**6 + 15*mu**4*sigma2 + 45*mu**2*sigma2**2 + 15*sigma2**3
        
        return moments
    
    @staticmethod
    def binary_moments(m: float, max_order: int = 4) -> Dict[int, float]:
        """
        Compute moments of binary variable σ ∈ {-1, +1} with ⟨σ⟩ = m.
        
        For binary variables: ⟨σ^k⟩ = m if k is odd, 1 if k is even
        """
        moments = {}
        
        for k in range(1, max_order + 1):
            if k % 2 == 0:
                moments[k] = 1.0  # Even powers
            else:
                moments[k] = m    # Odd powers
        
        return moments
    
    @staticmethod
    def mixed_moment_mean_field(moments_i: Dict[int, float], moments_j: Dict[int, float], 
                               power_i: int, power_j: int) -> float:
        """
        Compute mixed moment ⟨x_i^p * x_j^q⟩ using mean-field approximation.
        
        Mean-field: ⟨x_i^p * x_j^q⟩ ≈ ⟨x_i^p⟩ * ⟨x_j^q⟩
        """
        return moments_i[power_i] * moments_j[power_j]
    
    @staticmethod
    def demonstrate_gaussian_moments():
        """Demonstrate analytical Gaussian moment calculations."""
        print("=== Analytical Gaussian Moments Demonstration ===")
        
        # Example Gaussian parameters
        mu = 2.0
        sigma2 = 1.5
        sigma = math.sqrt(sigma2)
        
        print(f"Gaussian N(μ={mu}, σ²={sigma2}), σ={sigma:.3f}")
        print()
        
        # Compute analytical moments
        moments = AnalyticalMoments.gaussian_moments(mu, sigma2, max_order=6)
        
        print("Analytical Moments:")
        for k, moment in moments.items():
            print(f"  ⟨x^{k}⟩ = {moment:.6f}")
        
        print()
        
        # Verify with numerical sampling
        print("Numerical Verification (10M samples):")
        np.random.seed(42)
        samples = np.random.normal(mu, sigma, 10_000_000)
        
        for k in range(1, 7):
            numerical_moment = np.mean(samples**k)
            analytical_moment = moments[k]
            error = abs(numerical_moment - analytical_moment)
            print(f"  ⟨x^{k}⟩ = {numerical_moment:.6f} (error: {error:.6f})")
        
        print()
        print("=== Binary Variable Moments ===")
        
        # Binary variable example
        m = 0.3  # ⟨σ⟩ = 0.3
        binary_moments = AnalyticalMoments.binary_moments(m, max_order=6)
        
        print(f"Binary σ ∈ {{-1, +1}} with ⟨σ⟩ = {m}")
        print("Analytical Moments:")
        for k, moment in binary_moments.items():
            print(f"  ⟨σ^{k}⟩ = {moment:.6f}")

def create_polynomial_objective(coefficients: Dict[Tuple, float]) -> List[Dict]:
    """
    Create objective terms from polynomial coefficients.
    
    Args:
        coefficients: Dictionary mapping (var_ids, powers) to coefficients
                     e.g., {(0,): 1.0, (0, 0): 2.0, (0, 1): -1.5}
                     represents: 1.0*x_0 + 2.0*x_0^2 + (-1.5)*x_0*x_1
    
    Returns:
        List of objective term dictionaries for Enhanced FEM
    """
    objective_terms = []
    
    for var_power_tuple, coeff in coefficients.items():
        if len(var_power_tuple) == 1:
            # Single variable term
            var_id = var_power_tuple[0]
            power = 1  # Default power for single variable
            
            if power == 1:
                term_type = 'linear'
            elif power == 2:
                term_type = 'quadratic'
            elif power == 3:
                term_type = 'cubic'
            elif power == 4:
                term_type = 'quartic'
            else:
                raise ValueError(f"Unsupported power: {power}")
            
            objective_terms.append({
                'type': term_type,
                'variables': [var_id],
                'coefficient': coeff
            })
        
        elif len(var_power_tuple) == 2:
            # Two variable term (cross term)
            var_i, var_j = var_power_tuple
            
            if var_i == var_j:
                # Same variable: quadratic term
                objective_terms.append({
                    'type': 'quadratic',
                    'variables': [var_i],
                    'coefficient': coeff
                })
            else:
                # Cross term: x_i * x_j
                objective_terms.append({
                    'type': 'quadratic',
                    'variables': [var_i, var_j],
                    'coefficient': coeff
                })
        
        else:
            # Higher order terms
            total_power = len(var_power_tuple)
            if total_power == 3:
                term_type = 'cubic'
            elif total_power == 4:
                term_type = 'quartic'
            else:
                raise ValueError(f"Unsupported total power: {total_power}")
            
            objective_terms.append({
                'type': term_type,
                'variables': list(var_power_tuple),
                'coefficient': coeff
            })
    
    return objective_terms

def create_simple_polynomial_terms(linear_coeffs: Dict[int, float] = None,
                                 quadratic_coeffs: Dict[Tuple[int, int], float] = None,
                                 cubic_coeffs: Dict[Tuple[int, int, int], float] = None) -> List[Dict]:
    """
    Create polynomial objective terms in a more intuitive way.
    
    Args:
        linear_coeffs: {var_id: coefficient} for linear terms
        quadratic_coeffs: {(var_i, var_j): coefficient} for quadratic terms
        cubic_coeffs: {(var_i, var_j, var_k): coefficient} for cubic terms
    
    Returns:
        List of objective term dictionaries
    """
    objective_terms = []
    
    # Linear terms
    if linear_coeffs:
        for var_id, coeff in linear_coeffs.items():
            objective_terms.append({
                'type': 'linear',
                'variables': [var_id],
                'coefficient': coeff
            })
    
    # Quadratic terms
    if quadratic_coeffs:
        for (var_i, var_j), coeff in quadratic_coeffs.items():
            if var_i == var_j:
                # x_i^2 term
                objective_terms.append({
                    'type': 'quadratic',
                    'variables': [var_i],
                    'coefficient': coeff
                })
            else:
                # x_i * x_j cross term
                objective_terms.append({
                    'type': 'quadratic',
                    'variables': [var_i, var_j],
                    'coefficient': coeff
                })
    
    # Cubic terms
    if cubic_coeffs:
        for var_tuple, coeff in cubic_coeffs.items():
            objective_terms.append({
                'type': 'cubic',
                'variables': list(var_tuple),
                'coefficient': coeff
            })
    
    return objective_terms
